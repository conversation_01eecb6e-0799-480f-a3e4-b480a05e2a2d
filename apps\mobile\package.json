{"name": "mobile", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start", "prebuild": "expo prebuild --clean", "android": "expo run:android", "ios": "expo run:ios", "web": " expo start --web", "run:android": "expo run:android --device", "run:ios": "expo run:ios --device", "open:android": "open -a \"Android Studio\" ./android", "open:ios": "xed ./ios", "test": "jest --watchAll", "lint": "expo lint", "type-check": "tsc --noEmit", "clean": "rm -rf .expo .expo-shared android ios $TMPDIR/metro-cache && watchman watch-del-all || true"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@better-auth/expo": "^1.2.7", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.5", "@react-native-community/netinfo": "11.4.1", "@react-native-google-signin/google-signin": "^13.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@rn-primitives/avatar": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/types": "^1.1.0", "@tanstack/react-query": "^5.71.5", "babel-plugin-react-compiler": "^19.0.0-beta-af1b7da-20250417", "better-auth": "^1.2.7", "bottom-sheet-stepper": "^0.1.7", "date-fns": "^4.1.0", "eslint-plugin-react-compiler": "^19.1.0-rc.1", "expo": "^53.0.5", "expo-apple-authentication": "~7.2.4", "expo-application": "~6.1.4", "expo-asset": "~11.1.4", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.7", "expo-device": "~7.1.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-local-authentication": "~16.0.4", "expo-navigation-bar": "~4.2.4", "expo-passkey": "^0.1.0-rc.2", "expo-router": "~5.0.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "facebook-polyauth": "*", "google-polyauth": "*", "instagram-polyauth": "*", "lucide-react-native": "^0.487.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.2.1", "jest-expo": "~53.0.3", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "expo": {"autoLinking": {"nativeModulesDir": "../../node_modules"}}, "private": true}