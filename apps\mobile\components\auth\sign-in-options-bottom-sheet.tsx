import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { View, Text, Platform } from "react-native";
import BottomSheetStepper, { BottomSheetStepperRef } from "bottom-sheet-stepper";
import { AppleSignInButton } from "./apple-login-button";
import { GoogleLoginButton } from "./google-login-button";
import { PasskeyLoginButton } from "./passkey-login-button";

interface SignInOptionsBottomSheetProps {
  hasPasskey: boolean;
  passkeyAvailable: boolean;
  onGoogleSuccess: () => void;
  onGoogleError: (error: Error) => void;
  onAppleSuccess: () => void;
  onAppleError: (error: Error) => void;
  onPasskeySuccess: () => void;
  onPasskeyError: (error: Error) => void;
}

export interface SignInOptionsBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

export const SignInOptionsBottomSheet = forwardRef<
  SignInOptionsBottomSheetRef,
  SignInOptionsBottomSheetProps
>(({
  hasPasskey,
  passkeyAvailable,
  onGoogleSuccess,
  onGoogleError,
  onAppleSuccess,
  onAppleError,
  onPasskeySuccess,
  onPasskeyError,
}, ref) => {
  const stepperRef = useRef<BottomSheetStepperRef>(null);

  useImperativeHandle(ref, () => ({
    present: () => stepperRef.current?.present(),
    dismiss: () => stepperRef.current?.dismiss(),
  }));

  const SignInOptionsStep = () => (
    <View className="p-6">
      <Text className="text-xl font-semibold text-center mb-6 text-gray-900 dark:text-gray-100">
        More Sign In Options
      </Text>

      {/* Debug info - remove this later */}
      {__DEV__ && (
        <Text className="text-xs text-gray-500 mb-4 text-center">
          Debug: hasPasskey={hasPasskey.toString()}, passkeyAvailable={passkeyAvailable.toString()}, Platform={Platform.OS}
        </Text>
      )}

      <View className="gap-4">
        {/* Show Apple if on iOS and not the primary (has passkey) */}
        {Platform.OS === "ios" && hasPasskey && (
          <View>
            <AppleSignInButton
              onSuccess={onAppleSuccess}
              onError={onAppleError}
            />
          </View>
        )}

        {/* Show Google if it's not the primary (on iOS) or if passkey is the primary */}
        {(Platform.OS === "ios" || hasPasskey) && (
          <View>
            <GoogleLoginButton
              onSuccess={onGoogleSuccess}
              onError={onGoogleError}
            />
          </View>
        )}

        {/* Show PasskeyLoginButton only if not already the primary */}
        {!hasPasskey && (
          <View>
            <PasskeyLoginButton
              onSuccess={onPasskeySuccess}
              onError={onPasskeyError}
              isAvailable={passkeyAvailable}
              showSetupOption={!passkeyAvailable}
            />
          </View>
        )}
      </View>

      <View className="mt-6">
        <Text className="text-xs text-gray-500 text-center">
          By continuing, you agree to our Terms of Service and Privacy Policy.
        </Text>
      </View>
    </View>
  );

  return (
    <BottomSheetStepper
      ref={stepperRef}
      steps={[SignInOptionsStep]}
      disablePanDownToClose={false}
      disableBackDropPressToClose={false}
    />
  );
});

SignInOptionsBottomSheet.displayName = "SignInOptionsBottomSheet";
