# Instagram & Facebook OAuth Setup Guide

## 🎉 What's Been Created

I've successfully created Instagram and Facebook PolyAuth packages similar to your existing Google PolyAuth package. These packages are now integrated into both your `@apps/start-basic/` and `@apps/mobile/` applications.

## 📦 New Packages

### 1. Instagram PolyAuth (`packages/instagram-polyauth/`)
- **Server Plugin**: Uses Better Auth's `genericOAuth` plugin
- **Client Plugin**: Type-safe client companion
- **Multi-Client Support**: Framework for multiple Instagram client IDs
- **Custom User Info**: Fetches Instagram profile data

### 2. Facebook PolyAuth (`packages/facebook-polyauth/`)
- **Server Plugin**: Extends Better Auth's built-in Facebook provider
- **Client Plugin**: Type-safe client companion
- **Multi-Client Support**: Full token validation against multiple Facebook client IDs
- **Enhanced Security**: Uses Facebook's debug_token endpoint

## 🔧 Integration Complete

### ✅ Server-Side Integration
- Added Instagram and Facebook plugins to `apps/start-basic/src/lib/auth.ts`
- Updated environment schema in `apps/start-basic/src/lib/env.ts`
- Added providers to trusted providers list for account linking

### ✅ Client-Side Integration
- Added client plugins to `apps/start-basic/src/lib/auth-client.ts`
- Created Instagram and Facebook login button components
- Updated adaptive login form to include new providers

### ✅ Package Dependencies
- Added packages to both `apps/start-basic/package.json` and `apps/mobile/package.json`
- Packages are referenced as workspace dependencies with `"*"` version

## 🔑 Environment Variables Needed

Add these to your environment files:

```bash
# Instagram OAuth Configuration
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret
INSTAGRAM_IOS_CLIENT_ID=your-ios-instagram-client-id
INSTAGRAM_ANDROID_CLIENT_ID=your-android-instagram-client-id

# Facebook OAuth Configuration
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
FACEBOOK_IOS_CLIENT_ID=your-ios-facebook-client-id
FACEBOOK_ANDROID_CLIENT_ID=your-android-facebook-client-id
FACEBOOK_IOS_CLIENT_SECRET=your-ios-facebook-client-secret
FACEBOOK_ANDROID_CLIENT_SECRET=your-android-facebook-client-secret
```

## 🚀 How to Use

### 1. Set Up OAuth Apps

**Instagram:**
1. Go to [Instagram Basic Display](https://developers.facebook.com/docs/instagram-basic-display-api)
2. Create a new app and get your client ID and secret
3. Set redirect URI to: `http://localhost:3000/api/auth/oauth2/callback/instagram`

**Facebook:**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app and get your client ID and secret
3. Set redirect URI to: `http://localhost:3000/api/auth/callback/facebook`

### 2. Add Environment Variables
Add the credentials to your environment configuration.

### 3. Test the Integration
The login page will now show Instagram and Facebook buttons alongside Google and GitHub.

## 🎯 Features

### Multi-Client Support
Both packages support multiple client IDs for cross-platform authentication:
- Primary web app client ID
- iOS app client ID  
- Android app client ID
- Additional web app client IDs

### Conditional Loading
The providers only load if environment variables are provided, so your app won't break if credentials are missing.

### Mobile App Integration
The login form adapts based on the `auth_provider` parameter from mobile apps:
- `?auth_provider=instagram` - Shows only Instagram
- `?auth_provider=facebook` - Shows only Facebook
- `?auth_provider=all` - Shows all available providers

## 🔄 Next Steps

1. **Set up OAuth apps** on Instagram and Facebook developer portals
2. **Add environment variables** to your configuration
3. **Test the integration** by visiting `/login` in your app
4. **Configure mobile apps** to use the new providers

## 📱 Mobile Integration

The packages are ready for mobile integration. You can use them in your Expo app with Better Auth's Expo plugin, just like you currently do with Google OAuth.

## 🛡️ Security Notes

- **Facebook**: Requires client secrets for token validation (more secure but complex)
- **Instagram**: Uses access token validation (simpler but less secure)
- **Environment Variables**: Keep all secrets secure and never expose in client-side code

## 🎨 UI Integration

The login buttons follow your existing design patterns:
- **Instagram**: Purple-to-pink gradient matching Instagram branding
- **Facebook**: Blue background matching Facebook branding
- **Responsive**: Works on mobile and desktop with your existing grid layout

Your Instagram and Facebook OAuth integration is now complete and ready to use! 🎉
