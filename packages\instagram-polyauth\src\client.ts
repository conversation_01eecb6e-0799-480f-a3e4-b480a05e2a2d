import { BetterAuthClientPlugin } from "better-auth/client";
import type { instagramPolyAuth } from "./server.js";

type InstagramPolyAuthPlugin = typeof instagramPolyAuth;

/**
 * Client-side companion for the instagram-polyauth plugin.
 * This allows type-safe integration between the server and client plugins.
 */
export const instagramPolyAuthClient = () => {
  return {
    id: "instagram-polyauth",
    $InferServerPlugin: {} as ReturnType<InstagramPolyAuthPlugin>,
  } satisfies BetterAuthClientPlugin;
};
