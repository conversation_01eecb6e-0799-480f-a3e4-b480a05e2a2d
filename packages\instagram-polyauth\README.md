# Instagram PolyAuth

A Better-Auth plugin that extends Instagram OAuth to support multiple client IDs for token validation across your application ecosystem.

## Overview

When building multiple applications that share the same user base (different web apps, mobile apps, or a combination), you need to create separate client applications on the Instagram Developer Portal, each with its own client ID. Better Auth's standard Instagram integration only supports a single client ID, which creates authentication issues when your users try to sign in from different applications.

Instagram PolyAuth solves this by enabling token validation against multiple client IDs, providing a seamless authentication experience across your entire application ecosystem. Whether you're managing a suite of web applications, cross-platform deployments (web, iOS, Android), or white-labeled products, this plugin ensures unified authentication with minimal configuration.

## Features

- **Multi-Client Support** - Validate Instagram tokens against multiple client IDs
- **Unified User Base** - Share users seamlessly across multiple applications
- **Application Ecosystem** - Support authentication across web apps, mobile apps, and services
- **Type Safety** - Full TypeScript support with client/server type inference
- **Debug Logging** - Detailed logging for troubleshooting authentication issues
- **Graceful Fallback** - Attempts primary client ID validation first, then falls back to additional IDs
- **Simple Integration** - Easy integration with <PERSON> Auth using genericOAuth

## Usage

### Server-Side Setup

In your authentication configuration file:

```typescript
import { betterAuth } from "better-auth";
import { instagramPolyAuth } from "instagram-polyauth/server";

export const auth = betterAuth({
  // Other Better Auth options...

  plugins: [
    instagramPolyAuth({
      clientId: process.env.INSTAGRAM_PRIMARY_CLIENT_ID, // Primary client ID
      clientSecret: process.env.INSTAGRAM_CLIENT_SECRET, // Client secret
      additionalClientIds: [
        process.env.INSTAGRAM_APP2_CLIENT_ID, // Secondary web app
        process.env.INSTAGRAM_IOS_CLIENT_ID, // iOS app
        process.env.INSTAGRAM_ANDROID_CLIENT_ID, // Android app
      ],
      scopes: ["user_profile", "user_media"], // Requested scopes
    }),
    // Other plugins...
  ],
});
```

### Client-Side Setup

Import the client-side companion to ensure type safety:

```typescript
import { createAuthClient } from "better-auth/react";
import { instagramPolyAuthClient } from "instagram-polyauth/client";

export const authClient = createAuthClient({
  // Other Better Auth client options...

  plugins: [
    instagramPolyAuthClient(),
    // Other client plugins...
  ],
});
```

### Sign In with Instagram

```typescript
const signIn = async () => {
  const data = await authClient.signIn.social({
    provider: "instagram"
  });
};
```

## API Reference

### Server Plugin Options

The `instagramPolyAuth` function accepts the following options:

- `clientId`: Primary Instagram client ID
- `clientSecret`: Instagram client secret
- `additionalClientIds`: Array of additional client IDs to validate tokens against
- `scopes`: Array of Instagram scopes to request (default: ["user_profile", "user_media"])
- `redirectURI`: Custom redirect URI (optional)

### How It Works

1. When an Instagram access token is received, it first attempts validation with the primary client ID
2. If that fails and additional client IDs are configured, it attempts validation with each additional client ID
3. If validation succeeds with any client ID, authentication proceeds
4. User info is fetched from Instagram's Graph API

## Instagram API Limitations

- Instagram Basic Display API doesn't provide email addresses
- Profile pictures require additional API calls
- Token validation is done through the user info endpoint since Instagram doesn't have a dedicated token info endpoint

## Use Cases

- **Multi-Application Suites** - Support authentication across a suite of related web applications
- **White-Labeled Applications** - Enable authentication for multiple branded versions of your product
- **Cross-Platform Applications** - Seamless sign-in across web and mobile platforms
- **Development Environments** - Support different client IDs for your deployment pipeline
