import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get verification by identifier and value
export const getByIdentifierValue = query({
  args: { identifier: v.string(), value: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("verifications")
      .withIndex("by_identifier_value", (q) => 
        q.eq("identifier", args.identifier).eq("value", args.value)
      )
      .unique();
  },
});

// Create verification
export const create = mutation({
  args: {
    identifier: v.string(),
    value: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    return await ctx.db.insert("verifications", {
      ...args,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update verification
export const update = mutation({
  args: {
    id: v.id("verifications"),
    identifier: v.optional(v.string()),
    value: v.optional(v.string()),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Delete verification
export const remove = mutation({
  args: { id: v.id("verifications") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  },
});

// Delete verification by identifier and value
export const removeByIdentifierValue = mutation({
  args: { identifier: v.string(), value: v.string() },
  handler: async (ctx, args) => {
    const verification = await ctx.db
      .query("verifications")
      .withIndex("by_identifier_value", (q) => 
        q.eq("identifier", args.identifier).eq("value", args.value)
      )
      .unique();
    
    if (verification) {
      return await ctx.db.delete(verification._id);
    }
    return null;
  },
});

// Clean up expired verifications
export const cleanupExpired = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const expiredVerifications = await ctx.db
      .query("verifications")
      .withIndex("by_expires_at", (q) => q.lt("expiresAt", now))
      .collect();
    
    for (const verification of expiredVerifications) {
      await ctx.db.delete(verification._id);
    }
    
    return expiredVerifications.length;
  },
});

// Delete many verifications (for cleanup)
export const removeMany = mutation({
  args: { 
    identifier: v.optional(v.string()),
    expiredOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let verifications;
    
    if (args.expiredOnly) {
      const now = Date.now();
      verifications = await ctx.db
        .query("verifications")
        .withIndex("by_expires_at", (q) => q.lt("expiresAt", now))
        .collect();
    } else if (args.identifier) {
      verifications = await ctx.db
        .query("verifications")
        .filter((q) => q.eq(q.field("identifier"), args.identifier))
        .collect();
    } else {
      verifications = await ctx.db.query("verifications").collect();
    }
    
    for (const verification of verifications) {
      await ctx.db.delete(verification._id);
    }
    
    return verifications.length;
  },
});
