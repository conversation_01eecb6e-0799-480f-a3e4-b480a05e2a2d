{"name": "database", "version": "1.0.0", "description": "", "type": "module", "exports": {".": {"import": "./index.ts", "require": "./index.ts"}}, "scripts": {"build": "echo \"No build required for database package\"", "dev": "convex dev", "setup": "convex dev --until-success && convex dashboard"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"convex": "^1.18.0", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8"}, "devDependencies": {"@types/node": "^22"}}