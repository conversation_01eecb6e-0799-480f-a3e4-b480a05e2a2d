import React, { useState } from "react";
import { View, ScrollView, TouchableOpacity, Linking, Alert, Platform } from "react-native";
import { router } from "expo-router";
import { ArrowLeft, Check, Crown, Zap, Target } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useAuth } from "~/lib/auth-client";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "@react-navigation/native";

// Plan configurations matching your Autumn setup
const plans = [
  {
    id: "foundation_plan",
    name: "Foundation Plan",
    icon: Target,
    price: 9.99,
    yearlyPrice: 101.90,
    description: "Build your fitness foundation with intelligent AI guidance",
    popular: false,
    features: [
      "Complete onboarding assessment with long-term memory storage",
      "Sleep Time Compute - AI works in background to optimize your next workout",
      "Smart scheduling with automatic adjustments when life gets in the way",
      "Equipment photo analysis - Snap photos for instant workout alternatives",
      "Fridge scanning for basic meal suggestions from available ingredients",
      "Unlimited personalized workout programs",
      "Basic form guidance and injury prevention tips",
      "Progress tracking with weekly analytics",
      "Equipment adaptation for home or gym workouts",
      "3 detailed performance analysis reports per month",
      "Unlimited meal photo macro analysis with portion size detection",
      "15 grocery/fridge scans per month for meal planning",
      "Basic nutritional breakdown and meal suggestions",
      "5 personalized weekly meal plans",
      "Standard customer support"
    ]
  },
  {
    id: "performance_plan",
    name: "Performance Plan",
    icon: Zap,
    price: 19.99,
    yearlyPrice: 203.90,
    description: "Unlock advanced AI coaching for serious fitness growth",
    popular: true,
    features: [
      "Everything from Foundation Plan",
      "Advanced Sleep Time Compute - Predictive program adjustments",
      "Smart grocery intelligence - Track meals you can make from purchases",
      "Proactive notifications before workouts with preparation tips",
      "Dynamic equipment alternatives when your usual machines are unavailable",
      "Advanced form correction with video tutorials and biomechanics feedback",
      "Real-time adaptation based on performance patterns",
      "Recovery optimization recommendations",
      "Unlimited detailed progress analytics with trend analysis",
      "Weekly performance coaching insights",
      "Unlimited grocery/fridge scanning with meal countdown tracking",
      "Personalized nutrition recommendations based on training goals",
      "Custom meal plans adapted to your household ingredients",
      "Restaurant menu guidance and macro estimation",
      "Priority customer support (24-hour response)"
    ]
  },
  {
    id: "champion_plan",
    name: "Champion Plan",
    icon: Crown,
    price: 29.99,
    yearlyPrice: 305.90,
    description: "Elite AI coaching that anticipates your every fitness need",
    popular: false,
    features: [
      "Everything from Performance Plan",
      "Predictive Sleep Time Compute - AI anticipates needs before you do",
      "Advanced behavioral learning - Milo becomes your perfect training partner",
      "Intelligent meal prep forecasting - Never run out of healthy options",
      "Proactive lifestyle integration - Seamless adaptation to your changing schedule",
      "Advanced biomechanics analysis with AI-powered form optimization",
      "Plateau prevention with intelligent program periodization",
      "Competition preparation and peak performance protocols",
      "Real-time workout coaching with live form feedback",
      "Comprehensive health metrics integration with wearables",
      "AI training partner mode - conversational workout guidance",
      "AI meal prep coaching with shopping optimization",
      "Advanced metabolic adaptation tracking",
      "Personalized supplement recommendations",
      "Nutrition coaching conversations - ask Milo anything about your diet",
      "Integration with fitness goals for precision nutrition timing",
      "VIP customer support (same-day response)",
      "Early access to new AI features",
      "Monthly virtual coaching sessions",
      "Advanced progress reports and insights",
      "Priority feedback implementation"
    ]
  }
];

export default function PricingScreen() {
  const { colors } = useTheme();
  const { session } = useAuth();
  const [isYearly, setIsYearly] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSelectPlan = async (planId: string) => {
    if (!session?.user) {
      Alert.alert("Authentication Required", "Please log in to subscribe to a plan.");
      return;
    }

    setSelectedPlan(planId);
    setIsLoading(true);

    try {
      const plan = plans.find(p => p.id === planId);
      if (!plan) throw new Error("Plan not found");

      // Create checkout URL for web app
      const baseUrl = "http://localhost:3000"; // Replace with your actual web app URL
      const billing = isYearly ? 'yearly' : 'monthly';

      // Add success/cancel URLs for deep linking back to mobile
      const successUrl = `mobile://subscription-success?plan=${planId}`;
      const cancelUrl = `mobile://subscription-cancelled`;

      // Determine the OAuth provider used for authentication
      // This is a simplified approach - in a real app you'd store this info during login
      let authProvider = 'google'; // default
      if (Platform.OS === 'ios') {
        authProvider = 'apple'; // Apple is primary on iOS
      }

      // If user has passkey, we'll show all options
      // You could also check session.user.accounts to see which provider was used
      const hasPasskey = session?.user?.accounts?.some((account: any) => account.providerId === 'passkey');
      if (hasPasskey) {
        authProvider = 'all'; // Show all options if user has passkey
      }

      const checkoutUrl = `${baseUrl}/account/billing/checkout?plan=${planId}&billing=${billing}&from=mobile&auth_provider=${authProvider}&success_url=${encodeURIComponent(successUrl)}&cancel_url=${encodeURIComponent(cancelUrl)}`;

      const canOpen = await Linking.canOpenURL(checkoutUrl);
      if (canOpen) {
        await Linking.openURL(checkoutUrl);
      } else {
        Alert.alert("Error", "Unable to open payment page");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      Alert.alert("Error", "Failed to initiate payment process");
    } finally {
      setIsLoading(false);
      setSelectedPlan(null);
    }
  };

  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  const calculateYearlySavings = (monthlyPrice: number, yearlyPrice: number) => {
    const monthlyCost = monthlyPrice * 12;
    const savings = monthlyCost - yearlyPrice;
    const percentage = Math.round((savings / monthlyCost) * 100);
    return { savings: savings.toFixed(2), percentage };
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 border-b border-border">
        <TouchableOpacity onPress={() => router.back()} className="mr-3">
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text className="text-xl font-bold">Choose Your Plan</Text>
      </View>

      <ScrollView className="flex-1 px-4 py-6">
        {/* Header Section */}
        <View className="mb-6">
          <Text className="text-2xl font-bold text-center mb-2">
            Milo AI Fitness - Pricing Plans
          </Text>
          <Text className="text-center text-muted-foreground mb-4">
            Your AI fitness companion that grows stronger with you
          </Text>

          {/* Billing Toggle */}
          <View className="flex-row items-center justify-center mb-6">
            <TouchableOpacity
              onPress={() => setIsYearly(false)}
              className={`px-4 py-2 rounded-l-lg border ${!isYearly ? 'bg-primary border-primary' : 'bg-background border-border'}`}
            >
              <Text className={!isYearly ? 'text-primary-foreground' : 'text-foreground'}>
                Monthly
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setIsYearly(true)}
              className={`px-4 py-2 rounded-r-lg border ${isYearly ? 'bg-primary border-primary' : 'bg-background border-border'}`}
            >
              <Text className={isYearly ? 'text-primary-foreground' : 'text-foreground'}>
                Yearly (Save 15%)
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Plans */}
        <View className="space-y-4">
          {plans.map((plan) => {
            const IconComponent = plan.icon;
            const savings = calculateYearlySavings(plan.price, plan.yearlyPrice);
            const isProcessing = selectedPlan === plan.id && isLoading;

            return (
              <Card key={plan.id} className={`${plan.popular ? 'border-primary border-2' : ''}`}>
                {plan.popular && (
                  <View className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-primary">
                      <Text className="text-primary-foreground font-medium">Most Popular</Text>
                    </Badge>
                  </View>
                )}

                <CardHeader className="pb-4">
                  <View className="flex-row items-center mb-2">
                    <IconComponent size={24} color={colors.text} className="mr-2" />
                    <CardTitle className="text-lg">{plan.name}</CardTitle>
                  </View>

                  <View className="flex-row items-baseline mb-2">
                    <Text className="text-3xl font-bold">
                      ${isYearly ? formatPrice(plan.yearlyPrice / 12) : formatPrice(plan.price)}
                    </Text>
                    <Text className="text-muted-foreground ml-1">
                      /month
                    </Text>
                  </View>

                  {isYearly && (
                    <Text className="text-sm text-green-600">
                      Save ${savings.savings}/year ({savings.percentage}% off)
                    </Text>
                  )}

                  <Text className="text-muted-foreground text-sm">
                    {plan.description}
                  </Text>
                </CardHeader>

                <CardContent>
                  <View className="space-y-2 mb-6">
                    {plan.features.slice(0, 5).map((feature, index) => (
                      <View key={index} className="flex-row items-start">
                        <Check size={16} color="#22c55e" className="mr-2 mt-0.5" />
                        <Text className="text-sm flex-1">{feature}</Text>
                      </View>
                    ))}
                    {plan.features.length > 5 && (
                      <Text className="text-sm text-muted-foreground">
                        +{plan.features.length - 5} more features
                      </Text>
                    )}
                  </View>

                  <Button
                    onPress={() => handleSelectPlan(plan.id)}
                    disabled={isProcessing}
                    className={`w-full ${plan.popular ? 'bg-primary' : ''}`}
                  >
                    <Text className={`font-medium ${plan.popular ? 'text-primary-foreground' : ''}`}>
                      {isProcessing ? 'Processing...' : `Start 3-Day Free Trial`}
                    </Text>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </View>

        {/* Footer */}
        <View className="mt-8 mb-4">
          <Text className="text-center text-sm text-muted-foreground">
            All plans include a 3-day free trial. Cancel anytime.
          </Text>
          <Text className="text-center text-sm text-muted-foreground mt-2">
            You'll be redirected to our secure payment page to complete your subscription.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
