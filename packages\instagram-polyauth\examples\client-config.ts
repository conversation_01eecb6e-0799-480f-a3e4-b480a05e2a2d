// Example: How to use Instagram PolyAuth in your client configuration

import { createAuthClient } from "better-auth/react";
import { instagramPolyAuthClient } from "instagram-polyauth/client";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3000",
  
  plugins: [
    instagramPolyAuthClient(),
    // Other client plugins...
  ],
});

// Example: Sign in with Instagram
export const signInWithInstagram = async () => {
  try {
    const data = await authClient.signIn.social({
      provider: "instagram",
      callbackURL: "/dashboard", // Where to redirect after successful sign-in
    });
    
    console.log("Instagram sign-in successful:", data);
    return data;
  } catch (error) {
    console.error("Instagram sign-in failed:", error);
    throw error;
  }
};

// Example: Link Instagram account to existing user
export const linkInstagramAccount = async () => {
  try {
    const data = await authClient.oauth2.link({
      providerId: "instagram",
      callbackURL: "/account/linked", // Where to redirect after successful linking
    });
    
    console.log("Instagram account linked successfully:", data);
    return data;
  } catch (error) {
    console.error("Instagram account linking failed:", error);
    throw error;
  }
};
