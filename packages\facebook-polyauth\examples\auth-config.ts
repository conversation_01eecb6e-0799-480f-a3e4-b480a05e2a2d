// Example: How to use Facebook PolyAuth in your Better Auth configuration

import { betterAuth } from "better-auth";
import { facebookPolyAuth } from "facebook-polyauth/server";

export const auth = betterAuth({
  database: {
    // Your database configuration
  },
  
  plugins: [
    facebookPolyAuth({
      clientId: process.env.FACEBOOK_PRIMARY_CLIENT_ID!, // Primary client ID
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!, // Primary client secret
      additionalClientIds: [
        process.env.FACEBOOK_WEB2_CLIENT_ID!, // Secondary web app
        process.env.FACEBOOK_IOS_CLIENT_ID!, // iOS app
        process.env.FACEBOOK_ANDROID_CLIENT_ID!, // Android app
      ],
      additionalClientSecrets: [
        process.env.FACEBOOK_WEB2_CLIENT_SECRET!, // Secondary web app secret
        process.env.FACEBOOK_IOS_CLIENT_SECRET!, // iOS app secret
        process.env.FACEBOOK_ANDROID_CLIENT_SECRET!, // Android app secret
      ],
      scopes: ["email", "public_profile"], // Requested scopes
      
      // Optional: Custom fields to retrieve
      fields: ["id", "name", "email", "picture", "first_name", "last_name"],
    }),
    
    // Other plugins...
  ],
});
