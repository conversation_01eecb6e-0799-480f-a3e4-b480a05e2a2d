import { useState } from "react";
import { useAuth } from "~/lib/auth-client";
// TODO: Uncomment when Convex types are generated
// import { useQuery, useMutation } from "convex/react";
// import { api } from "convex/_generated/api";

interface FeatureAccessResult {
  allowed: boolean;
  hasSubscription: boolean;
  usage: {
    used: number;
    limit: number;
    resetDate: number;
  };
  planId: string;
}

interface UseFeatureAccessReturn {
  checkAccess: (featureId: string) => Promise<FeatureAccessResult>;
  trackUsage: (featureId: string, amount?: number) => Promise<void>;
  showPaywall: (featureId: string, description: string) => void;
  paywallVisible: boolean;
  paywallProps: {
    feature: string;
    featureDescription: string;
    currentPlan?: string;
    usageLimit?: {
      used: number;
      limit: number;
      resetDate?: string;
    };
  } | null;
  closePaywall: () => void;
}

export function useFeatureAccess(): UseFeatureAccessReturn {
  const { session } = useAuth();
  const [paywallVisible, setPaywallVisible] = useState(false);
  const [paywallProps, setPaywallProps] = useState<UseFeatureAccessReturn['paywallProps']>(null);

  // TODO: Uncomment when Convex types are generated
  // const checkFeatureAccess = useQuery(api.subscriptions.checkFeatureAccess, 
  //   session?.user?.id ? { userId: session.user.id, featureId: "" } : "skip"
  // );
  // const trackFeatureUsage = useMutation(api.subscriptions.trackFeatureUsage);

  const checkAccess = async (featureId: string): Promise<FeatureAccessResult> => {
    if (!session?.user?.id) {
      return {
        allowed: false,
        hasSubscription: false,
        usage: { used: 0, limit: 0, resetDate: Date.now() },
        planId: "free"
      };
    }

    // TODO: Replace with actual Convex query when types are generated
    // For now, return mock data
    const mockResult: FeatureAccessResult = {
      allowed: true, // Change to false to test paywall
      hasSubscription: true,
      usage: {
        used: 8,
        limit: 10,
        resetDate: Date.now() + 15 * 24 * 60 * 60 * 1000
      },
      planId: "foundation_plan"
    };

    return mockResult;

    // TODO: Uncomment when Convex is ready
    // try {
    //   const result = await checkFeatureAccess({ userId: session.user.id, featureId });
    //   return result;
    // } catch (error) {
    //   console.error("Error checking feature access:", error);
    //   return {
    //     allowed: false,
    //     hasSubscription: false,
    //     usage: { used: 0, limit: 0, resetDate: Date.now() },
    //     planId: "free"
    //   };
    // }
  };

  const trackUsage = async (featureId: string, amount: number = 1): Promise<void> => {
    if (!session?.user?.id) return;

    // TODO: Replace with actual Convex mutation when types are generated
    console.log(`Tracking usage for ${featureId}: ${amount}`);

    // TODO: Uncomment when Convex is ready
    // try {
    //   await trackFeatureUsage({
    //     userId: session.user.id,
    //     featureId,
    //     amount,
    //   });
    // } catch (error) {
    //   console.error("Error tracking feature usage:", error);
    // }
  };

  const showPaywall = (featureId: string, description: string) => {
    // Get current access info to show in paywall
    checkAccess(featureId).then((accessInfo) => {
      setPaywallProps({
        feature: featureId,
        featureDescription: description,
        currentPlan: accessInfo.planId,
        usageLimit: {
          used: accessInfo.usage.used,
          limit: accessInfo.usage.limit,
          resetDate: new Date(accessInfo.usage.resetDate).toLocaleDateString(),
        },
      });
      setPaywallVisible(true);
    });
  };

  const closePaywall = () => {
    setPaywallVisible(false);
    setPaywallProps(null);
  };

  return {
    checkAccess,
    trackUsage,
    showPaywall,
    paywallVisible,
    paywallProps,
    closePaywall,
  };
}

// Helper hook for easy feature gating
export function useFeatureGate(featureId: string, description: string) {
  const { checkAccess, trackUsage, showPaywall } = useFeatureAccess();

  const executeWithGate = async (action: () => void | Promise<void>) => {
    const access = await checkAccess(featureId);
    
    if (access.allowed) {
      await action();
      await trackUsage(featureId);
    } else {
      showPaywall(featureId, description);
    }
  };

  return { executeWithGate };
}

// Feature definitions for easy reference
export const FEATURES = {
  WORKOUT_PLANS: {
    id: "workout_plans",
    name: "Workout Plans",
    description: "Get personalized workout plans tailored to your fitness goals and available equipment."
  },
  MEAL_SCANS: {
    id: "meal_scans",
    name: "Meal Scanning",
    description: "Scan your meals to get detailed nutritional information and macro tracking."
  },
  FORM_GUIDANCE: {
    id: "form_guidance",
    name: "Form Guidance",
    description: "Get basic form tips and injury prevention advice for your exercises."
  },
  ADVANCED_FORM_GUIDANCE: {
    id: "advanced_form_guidance",
    name: "Advanced Form Guidance",
    description: "Access advanced form correction with video tutorials and biomechanics feedback."
  },
  PROGRESS_PHOTOS: {
    id: "progress_photos",
    name: "Progress Photos",
    description: "Track your fitness journey with progress photo analysis and comparisons."
  },
  AI_COACHING_SESSIONS: {
    id: "ai_coaching_sessions",
    name: "AI Coaching Sessions",
    description: "Get personalized coaching sessions with your AI fitness companion."
  },
  NUTRITION_COACHING: {
    id: "nutrition_coaching",
    name: "Nutrition Coaching",
    description: "Receive personalized nutrition advice and meal planning guidance."
  },
  MEAL_PLANNING: {
    id: "meal_planning",
    name: "Meal Planning",
    description: "Get custom meal plans based on your dietary preferences and goals."
  },
} as const;
