import { useEffect } from 'react'
import { useRouter } from '@tanstack/react-router'
import { useSession } from '@/lib/auth-client'
import LoginPage from '@/components/auth/login-page'

export default function LoginPageWithRedirect() {
  const { data: session, isPending } = useSession()
  const router = useRouter()

  useEffect(() => {
    // If we have a session and it's not pending, redirect to account page
    if (session?.user && !isPending) {
      router.navigate({ to: '/account' })
    }
  }, [session, isPending, router])

  // Show loading state while checking session
  if (isPending) {
    return (
      <div className="min-h-screen  bg-[#e9e5dc] dark:bg-[#1e1b16]  flex items-center justify-center  p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // If user is authenticated, don't render the login page (redirect will happen)
  if (session?.user) {
    return null
  }

  // If not authenticated, show the login page
  return <LoginPage />
}
