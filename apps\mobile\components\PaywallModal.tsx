import React from "react";
import { View, Modal, TouchableOpacity, ScrollView } from "react-native";
import { router } from "expo-router";
import { X, Crown, Zap, Target, ArrowRight } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useTheme } from "@react-navigation/native";

interface PaywallModalProps {
  visible: boolean;
  onClose: () => void;
  feature: string;
  featureDescription: string;
  currentPlan?: string;
  usageLimit?: {
    used: number;
    limit: number;
    resetDate?: string;
  };
}

const planBenefits = {
  foundation_plan: {
    name: "Foundation Plan",
    price: "$9.99/month",
    icon: Target,
    color: "#3b82f6",
    features: [
      "Unlimited personalized workout programs",
      "Basic form guidance and injury prevention",
      "15 grocery/fridge scans per month",
      "5 personalized weekly meal plans",
      "Standard customer support"
    ]
  },
  performance_plan: {
    name: "Performance Plan",
    price: "$19.99/month",
    icon: Zap,
    color: "#8b5cf6",
    features: [
      "Everything from Foundation Plan",
      "Advanced form correction with video tutorials",
      "Unlimited grocery/fridge scanning",
      "Custom meal plans adapted to your ingredients",
      "Priority customer support (24-hour response)"
    ]
  },
  champion_plan: {
    name: "Champion Plan",
    price: "$29.99/month",
    icon: Crown,
    color: "#f59e0b",
    features: [
      "Everything from Performance Plan",
      "AI training partner mode",
      "Advanced metabolic adaptation tracking",
      "Personalized supplement recommendations",
      "VIP customer support (same-day response)",
      "Early access to new AI features"
    ]
  }
};

export function PaywallModal({
  visible,
  onClose,
  feature,
  featureDescription,
  currentPlan,
  usageLimit
}: PaywallModalProps) {
  const { colors } = useTheme();

  // Determine which plan to recommend based on current plan
  const getRecommendedPlan = () => {
    if (!currentPlan || currentPlan === "free") {
      return "foundation_plan";
    }
    if (currentPlan === "foundation_plan") {
      return "performance_plan";
    }
    return "champion_plan";
  };

  const recommendedPlan = getRecommendedPlan();
  const planInfo = planBenefits[recommendedPlan as keyof typeof planBenefits];
  const IconComponent = planInfo.icon;

  const handleUpgrade = () => {
    onClose();
    router.push("/(flows)/pricing");
  };

  const getPaywallTitle = () => {
    if (usageLimit) {
      return `${feature} Limit Reached`;
    }
    return `Unlock ${feature}`;
  };

  const getPaywallMessage = () => {
    if (usageLimit) {
      return `You've used ${usageLimit.used} of ${usageLimit.limit} ${feature.toLowerCase()} this month. Upgrade to get unlimited access.`;
    }
    return `${featureDescription} Upgrade to unlock this premium feature.`;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-background rounded-t-3xl max-h-[80%]">
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-border">
            <Text className="text-lg font-bold">{getPaywallTitle()}</Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1 p-4">
            {/* Feature Description */}
            <View className="mb-6">
              <Text className="text-center text-muted-foreground">
                {getPaywallMessage()}
              </Text>
              
              {usageLimit?.resetDate && (
                <Text className="text-center text-sm text-muted-foreground mt-2">
                  Your limit resets on {usageLimit.resetDate}
                </Text>
              )}
            </View>

            {/* Recommended Plan */}
            <Card className="border-2" style={{ borderColor: planInfo.color }}>
              <CardHeader className="pb-4">
                <View className="flex-row items-center justify-between mb-2">
                  <View className="flex-row items-center">
                    <IconComponent size={24} color={planInfo.color} className="mr-2" />
                    <CardTitle className="text-lg">{planInfo.name}</CardTitle>
                  </View>
                  <Badge style={{ backgroundColor: planInfo.color }}>
                    <Text className="text-white font-medium">Recommended</Text>
                  </Badge>
                </View>
                
                <Text className="text-2xl font-bold" style={{ color: planInfo.color }}>
                  {planInfo.price}
                </Text>
              </CardHeader>
              
              <CardContent>
                <View className="space-y-2 mb-6">
                  {planInfo.features.map((feature, index) => (
                    <View key={index} className="flex-row items-start">
                      <View 
                        className="w-2 h-2 rounded-full mr-3 mt-2"
                        style={{ backgroundColor: planInfo.color }}
                      />
                      <Text className="text-sm flex-1">{feature}</Text>
                    </View>
                  ))}
                </View>
                
                <Button
                  onPress={handleUpgrade}
                  style={{ backgroundColor: planInfo.color }}
                  className="w-full"
                >
                  <View className="flex-row items-center justify-center">
                    <Text className="text-white font-medium mr-2">
                      Start 3-Day Free Trial
                    </Text>
                    <ArrowRight size={16} color="white" />
                  </View>
                </Button>
              </CardContent>
            </Card>

            {/* Alternative Plans */}
            <TouchableOpacity
              onPress={handleUpgrade}
              className="mt-4 p-4 border border-border rounded-lg"
            >
              <Text className="text-center text-primary font-medium">
                View All Plans & Pricing
              </Text>
            </TouchableOpacity>

            {/* Footer */}
            <View className="mt-6 mb-4">
              <Text className="text-center text-xs text-muted-foreground">
                All plans include a 3-day free trial. Cancel anytime.
              </Text>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}
