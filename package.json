{"name": "neb-starter", "version": "1.0.0", "description": "A production-ready universal app monorepo starter kit featuring a Next.js web app, Expo mobile app, pre-configured authentication with better-auth, and database integration with Prisma ORM - everything you need to start building cross-platform applications instantly.", "type": "module", "private": "true", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "bun --filter '*' dev", "dev:web": "bun --filter 'web' dev", "dev:web-tanstack": "bun --filter 'web-tanstack' dev", "dev:mobile": "bun --filter 'mobile' dev", "build": "bun --filter '*' build", "start": "bun --filter '*' start", "clean": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' + && rm -f bun.lock", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\" --ignore-path .gitignore", "type-check": "bun --filter '*' type-check", "lint": "eslint .", "lint:fix": "eslint . --fix", "postinstall": "echo \"Database migration to Convex completed\"", "android": "expo run:android", "ios": "expo run:ios"}, "devDependencies": {"@eslint/js": "^9", "cross-env": "^7.0.3", "dotenv-cli": "^8", "eslint": "^9", "eslint-plugin-react": "^7", "globals": "^16", "prettier": "^3", "typescript": "^5", "typescript-eslint": "^8"}, "resolutions": {"react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "@types/react": "~19.0.10", "globals": "16.0.0"}, "packageManager": "bun@latest", "engines": {"node": ">=18"}, "license": "MIT", "dependencies": {"autumn-js": "^0.0.46", "expo": "~53.0.9", "expo-asset": "^11.0.5", "expo-dev-client": "~5.1.8", "expo-linear-gradient": "~14.1.4", "react": "19.0.0", "react-native": "0.79.2", "uuid": "^11.1.0"}}