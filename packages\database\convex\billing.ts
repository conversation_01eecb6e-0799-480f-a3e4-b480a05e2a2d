// packages/database/convex/billing.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Get all active products
export const getProducts = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("products")
      .withIndex("by_active", (q) => q.eq("active", true))
      .collect();
  },
});

// Create a checkout session for mobile/web payments
export const createCheckoutSession = mutation({
  args: {
    userId: v.string(),
    productId: v.string(),
    customerEmail: v.string(),
    successUrl: v.string(),
    cancelUrl: v.string(),
    platform: v.string(),
  },
  handler: async (ctx, args) => {
    // Generate a unique session ID
    const sessionId = `cs_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create checkout session record
    await ctx.db.insert("checkout_sessions", {
      sessionId,
      userId: args.userId,
      productId: args.productId,
      platform: args.platform,
      status: "pending",
      checkoutUrl: `${process.env.VITE_APP_URL || 'http://localhost:3000'}/billing/checkout/${sessionId}`,
      successUrl: args.successUrl,
      cancelUrl: args.cancelUrl,
      expiresAt: Date.now() + (30 * 60 * 1000), // 30 minutes
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return {
      sessionId,
      checkoutUrl: `${process.env.VITE_APP_URL || 'http://localhost:3000'}/billing/checkout/${sessionId}`,
    };
  },
});

// Get checkout session details
export const getCheckoutSession = query({
  args: { sessionId: v.string() },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("checkout_sessions")
      .withIndex("by_session_id", (q) => q.eq("sessionId", args.sessionId))
      .first();

    if (!session) {
      return null;
    }

    // Check if session is expired (but don't update in query)
    if (session.expiresAt < Date.now() && session.status === "pending") {
      return { ...session, status: "expired" as const };
    }

    return session;
  },
});

// Separate mutation to expire checkout session
export const expireCheckoutSession = mutation({
  args: { sessionId: v.string() },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("checkout_sessions")
      .withIndex("by_session_id", (q) => q.eq("sessionId", args.sessionId))
      .first();

    if (!session) {
      throw new Error("Checkout session not found");
    }

    if (session.expiresAt < Date.now() && session.status === "pending") {
      await ctx.db.patch(session._id, {
        status: "expired",
        updatedAt: Date.now(),
      });
    }

    return session._id;
  },
});

// Update checkout session status
export const updateCheckoutSession = mutation({
  args: {
    sessionId: v.string(),
    status: v.union(v.literal("completed"), v.literal("expired"), v.literal("canceled")),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db
      .query("checkout_sessions")
      .withIndex("by_session_id", (q) => q.eq("sessionId", args.sessionId))
      .first();

    if (!session) {
      throw new Error("Checkout session not found");
    }

    await ctx.db.patch(session._id, {
      status: args.status,
      metadata: args.metadata,
      updatedAt: Date.now(),
    });

    return session._id;
  },
});

// Create or update a product
export const upsertProduct = mutation({
  args: {
    id: v.string(),
    name: v.string(),
    description: v.string(),
    price: v.number(),
    currency: v.string(),
    interval: v.union(v.literal("month"), v.literal("year")),
    features: v.array(v.string()),
    active: v.boolean(),
    stripeProductId: v.optional(v.string()),
    stripePriceId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("products")
      .withIndex("by_product_id", (q) => q.eq("id", args.id))
      .first();

    const productData = {
      id: args.id,
      name: args.name,
      description: args.description,
      price: args.price,
      currency: args.currency,
      interval: args.interval,
      features: args.features,
      active: args.active,
      stripeProductId: args.stripeProductId,
      stripePriceId: args.stripePriceId,
      updatedAt: Date.now(),
    };

    if (existing) {
      await ctx.db.patch(existing._id, productData);
      return existing._id;
    } else {
      return await ctx.db.insert("products", {
        ...productData,
        createdAt: Date.now(),
      });
    }
  },
});

// Record a billing event
export const recordBillingEvent = mutation({
  args: {
    userId: v.string(),
    type: v.union(
      v.literal("subscription_created"),
      v.literal("subscription_updated"),
      v.literal("subscription_canceled"),
      v.literal("payment_succeeded"),
      v.literal("payment_failed"),
      v.literal("invoice_created"),
      v.literal("checkout_session_completed")
    ),
    subscriptionId: v.optional(v.string()),
    externalId: v.string(),
    platform: v.string(),
    data: v.object({}),
  },
  handler: async (ctx, args) => {
    // Check if event already exists to prevent duplicates
    const existing = await ctx.db
      .query("billing_events")
      .withIndex("by_external_id", (q) => q.eq("externalId", args.externalId))
      .first();

    if (existing) {
      return existing._id;
    }

    return await ctx.db.insert("billing_events", {
      userId: args.userId,
      type: args.type,
      subscriptionId: args.subscriptionId,
      externalId: args.externalId,
      platform: args.platform,
      data: args.data,
      processed: false,
      createdAt: Date.now(),
    });
  },
});

// Get user's billing history
export const getUserBillingHistory = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const events = await ctx.db
      .query("billing_events")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(50);

    return events;
  },
});

// Record usage event for metered billing
export const recordUsageEvent = mutation({
  args: {
    userId: v.string(),
    featureId: v.string(),
    amount: v.number(),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("usage_events", {
      userId: args.userId,
      featureId: args.featureId,
      amount: args.amount,
      timestamp: Date.now(),
      metadata: args.metadata,
    });
  },
});