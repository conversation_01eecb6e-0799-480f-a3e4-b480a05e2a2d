{"name": "@nebstarter/cache", "version": "0.1.0", "description": "High-performance in-memory cache with advanced features for serverless and multi-environment support", "license": "MIT", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "./cache": {"types": "./dist/cache.d.ts", "import": "./dist/cache.js", "require": "./dist/cjs/cache.js"}, "./lru-map": {"types": "./dist/lru-map.d.ts", "import": "./dist/lru-map.js", "require": "./dist/cjs/lru-map.js"}}, "scripts": {"build": "yarn build:esm && yarn build:cjs", "build:esm": "tsc --outDir dist", "build:cjs": "tsc --outDir dist/cjs --module CommonJS --moduleResolution Node", "dev": "tsc --watch", "clean": "rm -rf dist"}, "files": ["dist/**/*"], "keywords": ["cache", "in-memory", "serverless", "performance"], "devDependencies": {"typescript": "^5"}}