import { facebook, FacebookOptions } from "better-auth/social-providers";
import { BetterAuthPlugin } from "better-auth";
import { OAuthProvider } from "better-auth/oauth2";

export interface FacebookPolyAuthOptions extends FacebookOptions {
  // Additional client IDs to accept for token validation
  additionalClientIds?: string[];
  // Additional client secrets corresponding to additional client IDs
  additionalClientSecrets?: string[];
}

/**
 * A plugin that extends the standard Facebook provider to support multiple client IDs
 * for token validation, which is especially useful for mobile applications.
 */
export const facebookPolyAuth = (options: FacebookPolyAuthOptions): BetterAuthPlugin => {
  return {
    id: "facebook-polyauth",

    init: (ctx) => {
      ctx.logger?.debug?.("Initializing facebook-polyauth plugin", {
        primaryClientId: options.clientId,
        additionalClientIdsCount: options.additionalClientIds?.length || 0,
      });

      // Create the standard Facebook provider
      const facebookProvider = facebook(options);

      // Extend the provider with our multi-client functionality
      if (facebookProvider.verifyIdToken) {
        const originalVerifyIdToken = facebookProvider.verifyIdToken;

        facebookProvider.verifyIdToken = async (token: string, nonce?: string) => {
          if (options.disableIdTokenSignIn) {
            return false;
          }

          if (options.verifyIdToken) {
            return options.verifyIdToken(token, nonce);
          }

          try {
            // Try the original verification first
            const isValid = await originalVerifyIdToken(token, nonce);
            if (isValid) {
              ctx.logger?.debug?.("Validated Facebook token with primary client ID");
              return true;
            }

            // If that fails and we have additional client IDs, try those
            if (options.additionalClientIds?.length) {
              // Facebook token validation using debug_token endpoint
              for (let i = 0; i < options.additionalClientIds.length; i++) {
                const clientId = options.additionalClientIds[i];
                const clientSecret = options.additionalClientSecrets?.[i];

                if (!clientSecret) {
                  ctx.logger?.debug?.("No client secret provided for additional client ID", {
                    clientId,
                  });
                  continue;
                }

                try {
                  // Get app access token for this client ID
                  const appTokenResponse = await fetch(
                    `https://graph.facebook.com/oauth/access_token?client_id=${clientId}&client_secret=${clientSecret}&grant_type=client_credentials`
                  );

                  if (!appTokenResponse.ok) {
                    ctx.logger?.debug?.("Failed to get app access token for additional client ID", {
                      clientId,
                    });
                    continue;
                  }

                  const appTokenData = await appTokenResponse.json();
                  const appAccessToken = appTokenData.access_token;

                  // Validate the user token using the app access token
                  const debugResponse = await fetch(
                    `https://graph.facebook.com/debug_token?input_token=${token}&access_token=${appAccessToken}`
                  );

                  if (!debugResponse.ok) {
                    ctx.logger?.debug?.("Failed to debug Facebook token for additional client ID", {
                      clientId,
                    });
                    continue;
                  }

                  const debugData = await debugResponse.json();

                  // Check if the token is valid and belongs to our app
                  if (
                    debugData.data &&
                    debugData.data.is_valid &&
                    debugData.data.app_id === clientId
                  ) {
                    ctx.logger?.debug?.("Validated Facebook token with additional client ID", {
                      clientId,
                    });
                    return true;
                  }
                } catch (error) {
                  ctx.logger?.debug?.("Facebook token validation failed for additional client ID", {
                    clientId,
                    error,
                  });
                }
              }
            }

            ctx.logger?.debug?.("No additional client IDs to try for Facebook token validation");
            return false;
          } catch (error) {
            ctx.logger?.error?.("Facebook ID token verification error", error);
            return false;
          }
        };
      }

      // Get existing socialProviders if available or create a new array
      const existingSocialProviders: OAuthProvider<any>[] = (ctx as any).socialProviders || [];

      // Check if there's already a Facebook provider
      const existingFacebookProviderIndex = existingSocialProviders.findIndex(
        (provider: OAuthProvider<any>) => provider.id === "facebook"
      );

      // Create a new array to avoid modifying the original
      const socialProviders = [...existingSocialProviders];

      // If a Facebook provider already exists, replace it; otherwise, add our new one
      if (existingFacebookProviderIndex !== -1) {
        ctx.logger?.debug?.(
          "Replacing existing Facebook provider with enhanced multi-client version"
        );
        socialProviders[existingFacebookProviderIndex] = facebookProvider;
      } else {
        ctx.logger?.debug?.("Adding Facebook polyauth provider");
        socialProviders.push(facebookProvider);
      }

      // Return the context with our modified social providers
      return {
        context: {
          socialProviders,
        },
      };
    },
  };
};
