import React from "react";
import { ScrollView, TouchableOpacity } from "react-native";
import { router } from "expo-router";
import { ArrowLeft } from "lucide-react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "@react-navigation/native";
import { View } from "react-native";
import { Text } from "~/components/ui/text";
import FeatureExample from "~/components/FeatureExample";

export default function FeatureExampleScreen() {
  const { colors } = useTheme();

  return (
    <SafeAreaView className="flex-1 bg-background">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 border-b border-border">
        <TouchableOpacity onPress={() => router.back()} className="mr-3">
          <ArrowLeft size={24} color={colors.text} />
        </TouchableOpacity>
        <Text className="text-xl font-bold">Feature Examples</Text>
      </View>

      <ScrollView className="flex-1">
        <FeatureExample />
      </ScrollView>
    </SafeAreaView>
  );
}
