import React from "react";
import { View, TouchableOpacity } from "react-native";
import { router } from "expo-router";
import { Crown, Zap, Target, ArrowRight, Calendar, CreditCard } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { useTheme } from "@react-navigation/native";

interface SubscriptionStatusProps {
  subscription?: {
    planId: string;
    status: string;
    currentPeriodEnd: string;
    cancelAtPeriodEnd?: boolean;
  };
  usage?: {
    [featureId: string]: {
      used: number;
      limit: number;
      resetDate: string;
    };
  };
}

const planInfo = {
  foundation_plan: {
    name: "Foundation Plan",
    icon: Target,
    color: "#3b82f6",
    description: "Build your fitness foundation"
  },
  performance_plan: {
    name: "Performance Plan",
    icon: Zap,
    color: "#8b5cf6",
    description: "Advanced AI coaching"
  },
  champion_plan: {
    name: "Champion Plan",
    icon: Crown,
    color: "#f59e0b",
    description: "Elite AI coaching"
  },
  free: {
    name: "Free Plan",
    icon: Target,
    color: "#6b7280",
    description: "Basic features"
  }
};

export function SubscriptionStatus({ subscription, usage }: SubscriptionStatusProps) {
  const { colors } = useTheme();
  
  const currentPlan = subscription?.planId || "free";
  const plan = planInfo[currentPlan as keyof typeof planInfo] || planInfo.free;
  const IconComponent = plan.icon;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = () => {
    if (!subscription) {
      return (
        <Badge variant="secondary">
          <Text>Free</Text>
        </Badge>
      );
    }

    switch (subscription.status) {
      case "active":
        return (
          <Badge style={{ backgroundColor: "#22c55e" }}>
            <Text className="text-white">Active</Text>
          </Badge>
        );
      case "trialing":
        return (
          <Badge style={{ backgroundColor: "#3b82f6" }}>
            <Text className="text-white">Free Trial</Text>
          </Badge>
        );
      case "canceled":
        return (
          <Badge variant="destructive">
            <Text>Cancelled</Text>
          </Badge>
        );
      case "past_due":
        return (
          <Badge style={{ backgroundColor: "#f59e0b" }}>
            <Text className="text-white">Past Due</Text>
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Text>{subscription.status}</Text>
          </Badge>
        );
    }
  };

  const handleManageSubscription = () => {
    // Navigate to web app billing page
    router.push("/(flows)/pricing");
  };

  const handleUpgrade = () => {
    router.push("/(flows)/pricing");
  };

  return (
    <View className="space-y-4">
      {/* Current Plan Card */}
      <Card>
        <CardHeader className="pb-4">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <IconComponent size={24} color={plan.color} className="mr-3" />
              <View>
                <CardTitle className="text-lg">{plan.name}</CardTitle>
                <Text className="text-sm text-muted-foreground">{plan.description}</Text>
              </View>
            </View>
            {getStatusBadge()}
          </View>
        </CardHeader>
        
        <CardContent>
          {subscription ? (
            <View className="space-y-3">
              <View className="flex-row items-center">
                <Calendar size={16} color={colors.text} className="mr-2" />
                <Text className="text-sm">
                  {subscription.cancelAtPeriodEnd 
                    ? `Expires on ${formatDate(subscription.currentPeriodEnd)}`
                    : `Renews on ${formatDate(subscription.currentPeriodEnd)}`
                  }
                </Text>
              </View>
              
              {subscription.cancelAtPeriodEnd && (
                <View className="p-3 bg-orange-50 dark:bg-orange-950 rounded-lg">
                  <Text className="text-sm text-orange-700 dark:text-orange-300">
                    Your subscription will end on {formatDate(subscription.currentPeriodEnd)}. 
                    Reactivate to continue enjoying premium features.
                  </Text>
                </View>
              )}
              
              <Button
                onPress={handleManageSubscription}
                variant="outline"
                className="w-full"
              >
                <View className="flex-row items-center justify-center">
                  <CreditCard size={16} color={colors.text} className="mr-2" />
                  <Text>Manage Subscription</Text>
                </View>
              </Button>
            </View>
          ) : (
            <View className="space-y-3">
              <Text className="text-sm text-muted-foreground">
                You're currently on the free plan. Upgrade to unlock premium features and unlimited access.
              </Text>
              
              <Button onPress={handleUpgrade} className="w-full">
                <View className="flex-row items-center justify-center">
                  <Text className="text-primary-foreground font-medium mr-2">
                    Upgrade Now
                  </Text>
                  <ArrowRight size={16} color="white" />
                </View>
              </Button>
            </View>
          )}
        </CardContent>
      </Card>

      {/* Usage Overview */}
      {usage && Object.keys(usage).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Usage This Month</CardTitle>
          </CardHeader>
          <CardContent>
            <View className="space-y-4">
              {Object.entries(usage).map(([featureId, featureUsage]) => {
                const percentage = (featureUsage.used / featureUsage.limit) * 100;
                const isNearLimit = percentage >= 80;
                
                return (
                  <View key={featureId}>
                    <View className="flex-row justify-between items-center mb-2">
                      <Text className="text-sm font-medium capitalize">
                        {featureId.replace('_', ' ')}
                      </Text>
                      <Text className="text-sm text-muted-foreground">
                        {featureUsage.used} / {featureUsage.limit}
                      </Text>
                    </View>
                    
                    <View className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <View
                        className={`h-2 rounded-full ${
                          isNearLimit ? 'bg-orange-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${Math.min(percentage, 100)}%` }}
                      />
                    </View>
                    
                    {isNearLimit && (
                      <Text className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                        Resets on {formatDate(featureUsage.resetDate)}
                      </Text>
                    )}
                  </View>
                );
              })}
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}
