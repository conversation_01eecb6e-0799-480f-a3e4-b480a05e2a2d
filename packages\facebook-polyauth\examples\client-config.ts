// Example: How to use Facebook PolyAuth in your client configuration

import { create<PERSON>uth<PERSON><PERSON> } from "better-auth/react";
import { facebookPolyAuthClient } from "facebook-polyauth/client";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3000",
  
  plugins: [
    facebookPolyAuthClient(),
    // Other client plugins...
  ],
});

// Example: Sign in with Facebook
export const signInWithFacebook = async () => {
  try {
    const data = await authClient.signIn.social({
      provider: "facebook",
      callbackURL: "/dashboard", // Where to redirect after successful sign-in
    });
    
    console.log("Facebook sign-in successful:", data);
    return data;
  } catch (error) {
    console.error("Facebook sign-in failed:", error);
    throw error;
  }
};

// Example: Sign in with Facebook ID Token (useful for mobile apps)
export const signInWithFacebookIdToken = async (idToken: string, accessToken?: string) => {
  try {
    const data = await authClient.signIn.social({
      provider: "facebook",
      idToken: {
        token: idToken,
        ...(accessToken && { accessToken }), // Include access token if available
      },
    });
    
    console.log("Facebook ID token sign-in successful:", data);
    return data;
  } catch (error) {
    console.error("Facebook ID token sign-in failed:", error);
    throw error;
  }
};

// Example: Link Facebook account to existing user
export const linkFacebookAccount = async () => {
  try {
    const data = await authClient.oauth2.link({
      providerId: "facebook",
      callbackURL: "/account/linked", // Where to redirect after successful linking
    });
    
    console.log("Facebook account linked successfully:", data);
    return data;
  } catch (error) {
    console.error("Facebook account linking failed:", error);
    throw error;
  }
};
