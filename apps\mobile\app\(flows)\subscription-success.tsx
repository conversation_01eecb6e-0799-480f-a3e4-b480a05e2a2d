import React, { useEffect } from "react";
import { View, TouchableOpacity } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { CheckCircle, ArrowRight } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "@react-navigation/native";

export default function SubscriptionSuccessScreen() {
  const { colors } = useTheme();
  const { plan } = useLocalSearchParams<{ plan?: string }>();

  // Get plan name from plan ID
  const getPlanName = (planId?: string) => {
    switch (planId) {
      case "foundation_plan":
        return "Foundation Plan";
      case "performance_plan":
        return "Performance Plan";
      case "champion_plan":
        return "Champion Plan";
      default:
        return "Premium Plan";
    }
  };

  const planName = getPlanName(plan);

  useEffect(() => {
    // TODO: Refresh user subscription status in your app state
    // This could involve refetching user data or updating local state
    console.log("Subscription successful for plan:", plan);
  }, [plan]);

  const handleContinue = () => {
    // Navigate back to the main app
    router.replace("/(tabs)/dashboard");
  };

  const handleViewBilling = () => {
    // Navigate to billing/subscription management
    router.push("/(tabs)/profile"); // Or wherever you handle subscription management
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-1 px-4 py-6 justify-center">
        <Card className="mx-4">
          <CardHeader className="items-center pb-4">
            <View className="mb-4">
              <CheckCircle size={64} color="#22c55e" />
            </View>
            <CardTitle className="text-2xl text-center">
              Welcome to {planName}!
            </CardTitle>
          </CardHeader>
          
          <CardContent className="items-center">
            <Text className="text-center text-muted-foreground mb-6">
              Your subscription has been activated successfully. You now have access to all premium features.
            </Text>
            
            <View className="w-full space-y-3">
              <Button onPress={handleContinue} className="w-full">
                <View className="flex-row items-center justify-center">
                  <Text className="text-primary-foreground font-medium mr-2">
                    Start Using Milo AI
                  </Text>
                  <ArrowRight size={16} color="white" />
                </View>
              </Button>
              
              <TouchableOpacity
                onPress={handleViewBilling}
                className="w-full py-3 items-center"
              >
                <Text className="text-primary">
                  View Subscription Details
                </Text>
              </TouchableOpacity>
            </View>
            
            <Text className="text-center text-sm text-muted-foreground mt-6">
              Your 3-day free trial has started. You won't be charged until the trial ends.
            </Text>
          </CardContent>
        </Card>
      </View>
    </SafeAreaView>
  );
}
