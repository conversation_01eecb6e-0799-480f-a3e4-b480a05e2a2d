@import "tailwindcss";



@layer base {
  html {
    color-scheme: light dark;
  }

  * {
    border-color: hsl(var(--border));
  }

  html,
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  .using-mouse * {
    outline: none !important;
  }

  /* Manrope Font Family */
  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-Regular.woff2") format("woff2"),
         url("/fonts/Manrope-Regular.ttf") format("truetype");
    font-display: swap;
    font-weight: 400;
    font-style: normal;
  }

  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-Medium.woff2") format("woff2");
    font-display: swap;
    font-weight: 500;
    font-style: normal;
  }

  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-SemiBold.woff2") format("woff2");
    font-display: swap;
    font-weight: 600;
    font-style: normal;
  }

  @font-face {
    font-family: "manrope_1";
    src: url("/fonts/Manrope-Bold.woff2") format("woff2");
    font-display: swap;
    font-weight: 700;
    font-style: normal;
  }

  /* Debug: Force font loading test */
  .font-manrope_1 {
    font-family: "manrope_1", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  }
}


/* Add this CSS to your global styles or component styles */
.react-modal-sheet-container {
  background-color: #e9e5dc !important;
}

/* For dark mode support */
@media (prefers-color-scheme: dark) {
  .react-modal-sheet-container {
    background-color: #1e1b16 !important;
  }
}

/* Or if you're using a dark mode class on html/body */
.dark .react-modal-sheet-container {
  background-color: #1e1b16 !important;
}