{"name": "web-tanstack", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "cross-env CONVEX_URL=https://famous-jay-93.convex.cloud NEXT_PUBLIC_CONVEX_URL=https://famous-jay-93.convex.cloud BETTER_AUTH_SECRET=kM34e7YIzX8BJs1F6HGWqKSOejLsaH8N VITE_APP_URL=http://localhost:3000 GOOGLE_WEB_CLIENT_ID=47633165209-v60el0ckjvivec2qujh9idmmsnq3i4c1.apps.googleusercontent.com GOOGLE_CLIENT_SECRET=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 GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/callback/google vinxi dev --port 3000", "build": "vinxi build", "start": "vinxi start --port 3000", "type-check": "tsc --noEmit"}, "dependencies": {"@better-auth/expo": "^1.2.7", "@nebstarter/cache": "*", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@simplewebauthn/server": "^13.1.1", "@tailwindcss/postcss": "^4.1.7", "@tanstack/react-router": "^1.120.11", "@tanstack/react-router-devtools": "^1.120.11", "@tanstack/react-start": "^1.120.11", "autumn-js": "^0.0.46", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.1", "database": "*", "facebook-polyauth": "*", "google-polyauth": "*", "instagram-polyauth": "*", "lucide-react": "^0.485.0", "motion": "^12.15.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-modal-sheet": "^4.4.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "vinxi": "0.5.3", "zod": "^3"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}}