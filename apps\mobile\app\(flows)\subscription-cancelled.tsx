import React from "react";
import { View, TouchableOpacity } from "react-native";
import { router } from "expo-router";
import { <PERSON>Cir<PERSON>, ArrowLeft, RefreshCw } from "lucide-react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { SafeAreaView } from "react-native-safe-area-context";
import { useTheme } from "@react-navigation/native";

export default function SubscriptionCancelledScreen() {
  const { colors } = useTheme();

  const handleTryAgain = () => {
    // Go back to pricing screen
    router.replace("/(flows)/pricing");
  };

  const handleGoBack = () => {
    // Navigate back to the main app
    router.replace("/(tabs)/dashboard");
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-1 px-4 py-6 justify-center">
        <Card className="mx-4">
          <CardHeader className="items-center pb-4">
            <View className="mb-4">
              <XCircle size={64} color="#ef4444" />
            </View>
            <CardTitle className="text-2xl text-center">
              Subscription Cancelled
            </CardTitle>
          </CardHeader>
          
          <CardContent className="items-center">
            <Text className="text-center text-muted-foreground mb-6">
              Your subscription process was cancelled. No charges were made to your account.
            </Text>
            
            <View className="w-full space-y-3">
              <Button onPress={handleTryAgain} className="w-full">
                <View className="flex-row items-center justify-center">
                  <RefreshCw size={16} color="white" className="mr-2" />
                  <Text className="text-primary-foreground font-medium">
                    Try Again
                  </Text>
                </View>
              </Button>
              
              <TouchableOpacity
                onPress={handleGoBack}
                className="w-full py-3 items-center"
              >
                <View className="flex-row items-center justify-center">
                  <ArrowLeft size={16} color={colors.text} className="mr-2" />
                  <Text className="text-foreground">
                    Back to Dashboard
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            
            <Text className="text-center text-sm text-muted-foreground mt-6">
              You can try subscribing again anytime. All plans include a 3-day free trial.
            </Text>
          </CardContent>
        </Card>
      </View>
    </SafeAreaView>
  );
}
