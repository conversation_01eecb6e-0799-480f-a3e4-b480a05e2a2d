import { useState, useEffect } from "react";
import { useRouter, useSearch } from "@tanstack/react-router";
import { Sheet } from "react-modal-sheet";
import { GoogleLoginButton } from "@/components/auth/google-login-button";
import { GithubLoginButton } from "@/components/auth/github-login-button";
import { InstagramLoginButton } from "@/components/auth/instagram-login-button";
import { FacebookLoginButton } from "@/components/auth/facebook-login-button";
import { ArrowLeft, Plus, X } from "lucide-react";
import { Footer } from "@/components/general/footer";
import { toast } from "@/components/ui/sonner";

interface AdaptiveLoginFormProps {
  callbackUrl?: string;
}

interface OAuthProvider {
  id: 'google' | 'github' | 'apple' | 'instagram' | 'facebook';
  name: string;
  component: React.ReactNode;
  available: boolean;
  isPrimary?: boolean;
}

// Mobile detection hook
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
}

// Device detection
function getDeviceType() {
  const userAgent = navigator.userAgent.toLowerCase();
  if (/iphone|ipad|ipod/.test(userAgent)) {
    return 'ios';
  } else if (/android/.test(userAgent)) {
    return 'android';
  }
  return 'desktop';
}

export default function AdaptiveLoginForm({ callbackUrl = "/account" }: AdaptiveLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const [currentImageLoaded, setCurrentImageLoaded] = useState(false);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });
  const isMobile = useIsMobile();
  const deviceType = getDeviceType();

  // Extract parameters from URL
  const from = (searchParams as any)?.from;
  const authProvider = (searchParams as any)?.auth_provider;
  const error = (searchParams as any)?.error;
  const isFromMobile = from === 'mobile';

  // Handle error parameters and show toast notifications
  useEffect(() => {
    console.log("=== DEBUGGING TOAST ===");
    console.log("searchParams:", searchParams);
    console.log("error value:", error);
    console.log("error type:", typeof error);
    console.log("Current URL:", window.location.href);

    // Parse URL manually to check for error parameter
    const urlParams = new URLSearchParams(window.location.search);
    const urlError = urlParams.get('error');
    console.log("Manual URL error parse:", urlError);
    console.log("All URL params:", Object.fromEntries(urlParams.entries()));

    // Check for authentication errors - use both router params and manual URL parsing
    const hasError = error === 'auth_failed' ||
                    error === 'unable_to_create_user' ||
                    urlError === 'unable_to_create_user' ||
                    urlError === 'auth_failed';

    console.log("hasError:", hasError);

    if (hasError) {
      console.log("🚨 SHOWING TOAST!");
      toast.error(
        "Account not found. Please sign up using the mobile app first.",
        "You need to create an account through our mobile app before signing in to the web dashboard."
      );

      // Clear the error parameter from URL after showing toast
      setTimeout(() => {
        console.log("Cleaning URL...");
        router.navigate({
          to: "/login",
          search: { from, auth_provider: authProvider },
          replace: true
        });
      }, 1000);
    } else {
      console.log("❌ No error detected, not showing toast");
    }
  }, [error, router, from, authProvider, searchParams]);

  // Handler for auth success
  const handleAuthSuccess = () => {
    router.navigate({ to: callbackUrl });
  };

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Authentication failed: ${error.message || "Unknown error"}`);
  };

  // Handler to go back to mobile app
  const handleBackToMobile = () => {
    const mobileUrl = "mobile://back";
    window.location.href = mobileUrl;
    setTimeout(() => {
      setErrorMessage("Please return to the mobile app to continue.");
    }, 1000);
  };

  // Define available OAuth providers
  const availableProviders: OAuthProvider[] = [
    {
      id: 'google' as const,
      name: 'Google',
      component: <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile || authProvider === 'google' || authProvider === 'all',
      isPrimary: deviceType === 'android' || (!isFromMobile && !isMobile)
    },
    {
      id: 'instagram' as const,
      name: 'Instagram',
      component: <InstagramLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile || authProvider === 'instagram' || authProvider === 'all'
    },
    {
      id: 'facebook' as const,
      name: 'Facebook',
      component: <FacebookLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile || authProvider === 'facebook' || authProvider === 'all'
    },
    {
      id: 'github' as const,
      name: 'GitHub',
      component: <GithubLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile // Only show GitHub for direct web access
    }
  ].filter(provider => provider.available);

  // Get primary and secondary providers for mobile
  const primaryProvider = availableProviders.find(p => p.isPrimary) || availableProviders[0];
  const secondaryProviders = availableProviders.filter(p => p.id !== primaryProvider?.id);

  // Track when the image is loaded
  useEffect(() => {
    setCurrentImageLoaded(false);
    const img = new Image();
    img.src = "/MiloSignin.png";
    img.onload = () => setCurrentImageLoaded(true);
    img.onerror = () => {
      console.warn("Failed to load MiloSignin.png image");
      setCurrentImageLoaded(true);
    };
  }, []);

  return (
    <div className="w-full min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16] py-0 md:py-8 flex items-center justify-center">
      {/* Main container - full width on mobile, constrained on desktop */}
      <div className="w-full min-h-screen md:min-h-[88vh] md:max-w-md lg:max-w-lg xl:max-w-xl md:rounded-xl md:shadow-lg relative md:overflow-hidden">
        
        {/* Navigation bar - only show if from mobile */}
        {isFromMobile && (
          <div className="fixed top-0 left-0 right-0 h-20 backdrop-blur-sm justify-between px-4 z-[100005] items-center flex md:absolute md:top-0">
            <div className="font-manrope_1 flex gap-4 items-center">
              <span className="text-black dark:text-white text-base font-manrope_1 font-bold">Mobile Login</span>
            </div>
            <button
              type="button"
              onClick={handleBackToMobile}
              className="flex items-center text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-base font-manrope_1"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Mobile App
            </button>
          </div>
        )}

        {/* Background Image */}
        <div className="absolute inset-0 overflow-hidden">
          <img
            src="/MiloSignin.png"
            alt="Milo Sign In"
            className="w-full h-full object-cover opacity-80"
          />
          {/* Gradient overlay */}
          <div 
            className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[#e9e5dc] dark:to-[#1e1b16]" 
            style={{
              background: `linear-gradient(to bottom, 
                transparent 0%, 
                rgba(233,229,220,0.1) 10%, 
                rgba(233,229,220,0.3) 30%, 
                rgba(233,229,220,0.6) 50%, 
                rgba(233,229,220,1) 80%, 
                rgba(233,229,220,1) 100%)`
            }} 
          />
        </div>

        {/* Main content container - positioned at bottom */}
        <div className="absolute bottom-0 left-0 right-0 z-10">
          <div className="px-0 pb-2">
            {/* Header text */}
            <div className="text-center mb-4">
              <h1 className="text-4xl font-semibold text-black dark:text-white font-manrope_1 mb-2">
                {isFromMobile ? (
                  <>Welcome back</>
                ) : (
                  <>Sign in</>
                )}
              </h1>
              <p className="text-sm px-4 md:text-lg md:px-12 text-black/60 dark:text-white/60 font-manrope_1">
                {isFromMobile
                  ? "Continue your fitness journey"
                  : "Secure access for existing members to the admin dashboard and premium features"
                }
              </p>
            </div>

            {/* Error message */}
            {errorMessage && (
              <div className="w-full p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 mb-4">
                <p className="text-red-700 dark:text-red-300 text-sm font-manrope_1">{errorMessage}</p>
              </div>
            )}

  {/* OAuth Buttons Section */}
<div className="w-full border-b mb-1 border-black/20"/>
<div className="w-full px-2 md:px-4  mx-auto">
  {/* Unified Layout: Primary + Plus button for all devices when multiple providers */}
  {secondaryProviders.length > 0 ? (
    <div className="flex items-center justify-between">
      {/* Primary OAuth Button - Far Left */}
      <div className="flex justify-start">
        {primaryProvider?.component}
      </div>
      
      {/* Plus button for more options - Far Right */}
      <div className="flex justify-end">
        <button
          onClick={() => setIsBottomSheetOpen(true)}
          className="h-12 px-2 cursor-pointer rounded-lg flex items-center justify-center gap-1 transition-shadow"
        >
          <span className="text-sm font-manrope_1 text-gray-600 dark:text-gray-300">More options</span>
          <Plus className="w-5 h-5 text-gray-600 dark:text-gray-300" />
        </button>
      </div>
    </div>
  ) : (
    /* Single provider: Just show the one button */
    <div className="w-full">
      {primaryProvider?.component}
    </div>
  )}
</div>
          </div>
        </div>

        {/* Bottom Sheet for additional OAuth options - Responsive positioning */}
        <div className={`${!isMobile ? 'absolute inset-0' : ''}`}>
          <Sheet 
            isOpen={isBottomSheetOpen} 
            onClose={() => setIsBottomSheetOpen(false)}
            detent="content-height"
            className="md:max-w-md lg:max-w-lg xl:max-w-xl md:fixed md:mb-12 md:rounded-b-xl md:bottom-0 md:left-0 md:right-0  mx-auto"
          >
            <Sheet.Container className={!isMobile ? 'relative !fixed-none  ' : ''}>
              <Sheet.Header>
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-manrope_1">
                    More sign-in options
                  </h3>
                  <button
                    onClick={() => setIsBottomSheetOpen(false)}
                    className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </Sheet.Header>
              <Sheet.Content>
                <div className="pb-8">
                  {/* OAuth options with top border */}
                  <div className="border-t border-gray-500 dark:border-gray-500 pt-4">
                    <div className="px-4 space-y-3">
                      {secondaryProviders.map((provider) => (
                        <div key={provider.id} className="w-full">
                          {provider.component}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Footer at the bottom - only on mobile */}
                  {isMobile && (
                    <div className="mt-6 px-4">
                      <Footer/>
                    </div>
                  )}
                </div>
              </Sheet.Content>
            </Sheet.Container>
            <Sheet.Backdrop 
              onClick={() => setIsBottomSheetOpen(false)} 
              className={!isMobile ? 'absolute inset-0' : ''}
            />
          </Sheet>
        </div>

        {/* Footer - positioned at very bottom */}
        <div className="hidden md:block absolute bottom-0 left-0 right-0 z-5">
          <Footer />
        </div>
      </div>
    </div>
  );
}