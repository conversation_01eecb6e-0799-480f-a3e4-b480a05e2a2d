import { Tabs } from "expo-router";
import { User, LayoutDashboard } from "lucide-react-native";
import { Image, Pressable, View } from "react-native";
import { useColorScheme } from "~/lib/useColorScheme";
import { ThemeToggle } from "~/components/ui/theme-toggle";
import { PlatformPressable } from "@react-navigation/elements";

export default function TabsLayout() {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        headerShown: true,
        headerStyle: {
          height: 100,
        },
        tabBarStyle: {
          backgroundColor: isDarkColorScheme ? "rgb(21, 21, 24)" : "rgb(255, 255, 255)",
        },
        tabBarButton: (props) => (
          <PlatformPressable {...props} android_ripple={{ color: "transparent" }} />
        ),
        headerLeft: () => (
          <View className="flex-row items-center ml-8">
            <Pressable>
              <Image
                source={
                  isDarkColorScheme
                    ? require("~/assets/images/logo.png")
                    : require("~/assets/images/logo.png")
                }
                className="w-12 h-12"
                resizeMode="contain"
              />
            </Pressable>
          </View>
        ),
        headerTitle: "",
        headerRight: () => (
          <View className="mr-8">
            <ThemeToggle />
          </View>
        ),
      }}
    >
      <Tabs.Screen
        name="dashboard"
        options={{
          title: "Dashboard",
          tabBarIcon: ({ color }) => <LayoutDashboard size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
          tabBarIcon: ({ color }) => <User size={24} color={color} />,
        }}
      />
    </Tabs>
  );
}
