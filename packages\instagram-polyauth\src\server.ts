import { BetterAuthPlugin } from "better-auth";
import { genericOAuth } from "better-auth/plugins";

export interface InstagramPolyAuthOptions {
  clientId: string;
  clientSecret: string;
  // Additional client IDs to accept for token validation
  additionalClientIds?: string[];
  scopes?: string[];
  redirectURI?: string;
  // Custom function to fetch user info
  getUserInfo?: (tokens: any) => Promise<any>;
  // Custom function to map profile to user
  mapProfileToUser?: (profile: any) => Promise<any>;
}

/**
 * A plugin that extends Instagram OAuth to support multiple client IDs
 * for token validation, which is especially useful for mobile applications.
 */
export const instagramPolyAuth = (options: InstagramPolyAuthOptions): BetterAuthPlugin => {
  // Create the base Instagram configuration for genericOAuth
  const instagramConfig = {
    providerId: "instagram",
    clientId: options.clientId,
    clientSecret: options.clientSecret,
    authorizationUrl: "https://api.instagram.com/oauth/authorize",
    tokenUrl: "https://api.instagram.com/oauth/access_token",
    userInfoUrl: "https://graph.instagram.com/me",
    scopes: options.scopes || ["user_profile", "user_media"],
    redirectURI: options.redirectURI,

    // Custom user info fetching for Instagram
    getUserInfo: options.getUserInfo || (async (tokens: any) => {
      try {
        const response = await fetch(
          `https://graph.instagram.com/me?fields=id,username,account_type&access_token=${tokens.access_token}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch Instagram user info");
        }

        const userInfo = await response.json();

        return {
          id: userInfo.id,
          name: userInfo.username,
          email: null, // Instagram doesn't provide email
          image: null, // Would need additional API call for profile picture
          emailVerified: false,
        };
      } catch (error) {
        console.error("Instagram user info fetch error", error);
        return null;
      }
    }),

    // Map profile to user format
    mapProfileToUser: options.mapProfileToUser || (async (profile: any) => {
      return {
        firstName: profile.name,
        lastName: "",
      };
    }),
  };

  // Create the base genericOAuth plugin
  const basePlugin = genericOAuth({
    config: [instagramConfig]
  });

  // Return our enhanced plugin that wraps the base plugin
  return {
    ...basePlugin,
    id: "instagram-polyauth",

    init: (ctx) => {
      ctx.logger?.debug?.("Initializing instagram-polyauth plugin", {
        primaryClientId: options.clientId,
        additionalClientIdsCount: options.additionalClientIds?.length || 0,
      });

      // Initialize the base plugin first
      const baseResult = basePlugin.init?.(ctx);

      // If we have additional client IDs, we would need to implement
      // custom token validation logic here
      if (options.additionalClientIds?.length) {
        ctx.logger?.debug?.("Instagram polyauth with additional client IDs configured", {
          additionalClientIds: options.additionalClientIds,
        });

        // Note: Instagram token validation with multiple client IDs is complex
        // because Instagram doesn't have a simple token info endpoint like Google
        // This would require storing app secrets and implementing custom validation
        ctx.logger?.warn?.("Multi-client Instagram token validation not yet fully implemented");
      }

      return baseResult;
    },
  };
};
