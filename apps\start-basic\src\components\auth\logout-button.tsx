import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LogOut, Loader2 } from "lucide-react";
import { signOut } from "@/lib/auth-client";
import { useRouter } from "@tanstack/react-router";

// Import button component types
type ButtonProps = React.ComponentPropsWithoutRef<typeof Button>;
type ButtonVariant = NonNullable<ButtonProps["variant"]>;
type ButtonSize = NonNullable<ButtonProps["size"]>;

interface LogOutButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
}

export function LogOutButton({
  variant = "ghost",
  size = "default",
  className = "",
}: LogOutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      setIsLoading(true);
      await signOut({
        fetchOptions: {
          onSuccess: () => {
            router.navigate({ to: "/login" });
          },
        },
      });
    } catch (error) {
      console.error("Sign out error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleSignOut}
      disabled={isLoading}
    >
      {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <LogOut className="h-4 w-4" />}
      {size !== "icon" && <span className="ml-2">{isLoading ? "Signing out..." : "Sign out"}</span>}
    </Button>
  );
}
