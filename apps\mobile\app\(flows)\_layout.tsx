import { Stack } from "expo-router";

export default function FlowsLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="update-profile" options={{ headerShown: false }} />
      <Stack.Screen name="pricing" options={{ headerShown: false }} />
      <Stack.Screen name="subscription-success" options={{ headerShown: false }} />
      <Stack.Screen name="subscription-cancelled" options={{ headerShown: false }} />
      <Stack.Screen name="feature-example" options={{ headerShown: false }} />
    </Stack>
  );
}
