import React from "react";
import { View, TouchableOpacity, Alert } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { <PERSON>, Dumbbell, <PERSON>, Brain } from "lucide-react-native";
import { useFeatureGate, useFeatureAccess, FEATURES } from "~/hooks/useFeatureAccess";
import { PaywallModal } from "~/components/PaywallModal";
import { useTheme } from "@react-navigation/native";

/**
 * Example component showing how to implement feature gating with paywalls
 * This demonstrates the pattern you should use throughout your app
 */
export default function FeatureExample() {
  const { colors } = useTheme();
  const { paywallVisible, paywallProps, closePaywall } = useFeatureAccess();
  
  // Example feature gates
  const workoutPlanGate = useFeatureGate(
    FEATURES.WORKOUT_PLANS.id,
    FEATURES.WORKOUT_PLANS.description
  );
  
  const mealScanGate = useFeatureGate(
    FEATURES.MEAL_SCANS.id,
    FEATURES.MEAL_SCANS.description
  );
  
  const advancedFormGate = useFeatureGate(
    FEATURES.ADVANCED_FORM_GUIDANCE.id,
    FEATURES.ADVANCED_FORM_GUIDANCE.description
  );
  
  const aiCoachingGate = useFeatureGate(
    FEATURES.AI_COACHING_SESSIONS.id,
    FEATURES.AI_COACHING_SESSIONS.description
  );

  // Example feature actions
  const generateWorkoutPlan = async () => {
    // This will check access and show paywall if needed
    await workoutPlanGate.executeWithGate(async () => {
      // Your actual feature logic here
      Alert.alert("Success!", "Generating your personalized workout plan...");
      console.log("Generating workout plan...");
    });
  };

  const scanMeal = async () => {
    await mealScanGate.executeWithGate(async () => {
      Alert.alert("Success!", "Opening camera to scan your meal...");
      console.log("Opening meal scanner...");
    });
  };

  const getAdvancedFormGuidance = async () => {
    await advancedFormGate.executeWithGate(async () => {
      Alert.alert("Success!", "Loading advanced form analysis...");
      console.log("Loading advanced form guidance...");
    });
  };

  const startAICoaching = async () => {
    await aiCoachingGate.executeWithGate(async () => {
      Alert.alert("Success!", "Starting your AI coaching session...");
      console.log("Starting AI coaching session...");
    });
  };

  return (
    <View className="flex-1 p-4 space-y-4">
      <Text className="text-2xl font-bold mb-4">Feature Examples</Text>
      <Text className="text-muted-foreground mb-6">
        Tap any feature below to see the paywall system in action. 
        Features will check your subscription status and usage limits.
      </Text>

      {/* Workout Plans Feature */}
      <Card>
        <CardHeader>
          <View className="flex-row items-center">
            <Dumbbell size={24} color={colors.text} className="mr-3" />
            <CardTitle>Generate Workout Plan</CardTitle>
          </View>
        </CardHeader>
        <CardContent>
          <Text className="text-muted-foreground mb-4">
            Get a personalized workout plan based on your goals and available equipment.
          </Text>
          <Button onPress={generateWorkoutPlan}>
            <Text className="text-primary-foreground">Generate Plan</Text>
          </Button>
        </CardContent>
      </Card>

      {/* Meal Scanning Feature */}
      <Card>
        <CardHeader>
          <View className="flex-row items-center">
            <Camera size={24} color={colors.text} className="mr-3" />
            <CardTitle>Scan Meal</CardTitle>
          </View>
        </CardHeader>
        <CardContent>
          <Text className="text-muted-foreground mb-4">
            Scan your meal to get detailed nutritional information and macro tracking.
          </Text>
          <Button onPress={scanMeal}>
            <Text className="text-primary-foreground">Scan Meal</Text>
          </Button>
        </CardContent>
      </Card>

      {/* Advanced Form Guidance Feature */}
      <Card>
        <CardHeader>
          <View className="flex-row items-center">
            <Apple size={24} color={colors.text} className="mr-3" />
            <CardTitle>Advanced Form Analysis</CardTitle>
          </View>
        </CardHeader>
        <CardContent>
          <Text className="text-muted-foreground mb-4">
            Get advanced form correction with video tutorials and biomechanics feedback.
          </Text>
          <Button onPress={getAdvancedFormGuidance}>
            <Text className="text-primary-foreground">Analyze Form</Text>
          </Button>
        </CardContent>
      </Card>

      {/* AI Coaching Feature */}
      <Card>
        <CardHeader>
          <View className="flex-row items-center">
            <Brain size={24} color={colors.text} className="mr-3" />
            <CardTitle>AI Coaching Session</CardTitle>
          </View>
        </CardHeader>
        <CardContent>
          <Text className="text-muted-foreground mb-4">
            Start a personalized coaching session with your AI fitness companion.
          </Text>
          <Button onPress={startAICoaching}>
            <Text className="text-primary-foreground">Start Session</Text>
          </Button>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>How to Implement</CardTitle>
        </CardHeader>
        <CardContent>
          <Text className="text-sm text-muted-foreground">
            1. Import useFeatureGate and FEATURES from ~/hooks/useFeatureAccess{'\n'}
            2. Create a feature gate: useFeatureGate(FEATURES.YOUR_FEATURE.id, FEATURES.YOUR_FEATURE.description){'\n'}
            3. Wrap your feature action: await featureGate.executeWithGate(() => yourAction()){'\n'}
            4. Add PaywallModal to your component with useFeatureAccess hook
          </Text>
        </CardContent>
      </Card>

      {/* Paywall Modal */}
      {paywallProps && (
        <PaywallModal
          visible={paywallVisible}
          onClose={closePaywall}
          feature={paywallProps.feature}
          featureDescription={paywallProps.featureDescription}
          currentPlan={paywallProps.currentPlan}
          usageLimit={paywallProps.usageLimit}
        />
      )}
    </View>
  );
}
