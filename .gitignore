
# dependencies
node_modules

# debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# ides
.idea
.vs

# Build artifacts
dist
.build

# misc
.DS_Store
.env.local
.env.development.local
.env.production.local
guide.md
.nx
.husky
guide.md
notes.md
uploads/
patch.js


# next.js
/.next/
/out/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Ignore all .env files
.env*
# Allow .env.sample to be committed
!.env.sample

# typescript
*.tsbuildinfo

app-example

android
ios