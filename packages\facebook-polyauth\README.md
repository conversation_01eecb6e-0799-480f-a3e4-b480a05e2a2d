# Facebook PolyAuth

A Better-Auth plugin that extends the standard Facebook provider to support multiple client IDs for token validation across your application ecosystem.

## Overview

When building multiple applications that share the same user base (different web apps, mobile apps, or a combination), you need to create separate client applications on the Facebook Developer Portal, each with its own client ID. Better Auth's standard Facebook provider only supports a single client ID, which creates authentication issues when your users try to sign in from different applications.

Facebook PolyAuth solves this by enabling token validation against multiple client IDs, providing a seamless authentication experience across your entire application ecosystem. Whether you're managing a suite of web applications, cross-platform deployments (web, iOS, Android), or white-labeled products, this plugin ensures unified authentication with minimal configuration.

## Features

- **Multi-Client Support** - Validate Facebook tokens against multiple client IDs
- **Unified User Base** - Share users seamlessly across multiple applications
- **Application Ecosystem** - Support authentication across web apps, mobile apps, and services
- **Type Safety** - Full TypeScript support with client/server type inference
- **Debug Logging** - Detailed logging for troubleshooting authentication issues
- **Graceful Fallback** - Attempts primary client ID validation first, then falls back to additional IDs
- **Simple Integration** - Drop-in replacement for the standard Facebook provider

## Usage

### Server-Side Setup

In your authentication configuration file:

```typescript
import { betterAuth } from "better-auth";
import { facebookPolyAuth } from "facebook-polyauth/server";

export const auth = betterAuth({
  // Other Better Auth options...

  plugins: [
    facebookPolyAuth({
      clientId: process.env.FACEBOOK_PRIMARY_CLIENT_ID, // Primary client ID
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET, // Primary client secret
      additionalClientIds: [
        process.env.FACEBOOK_APP2_CLIENT_ID, // Secondary web app
        process.env.FACEBOOK_IOS_CLIENT_ID, // iOS app
        process.env.FACEBOOK_ANDROID_CLIENT_ID, // Android app
      ],
      additionalClientSecrets: [
        process.env.FACEBOOK_APP2_CLIENT_SECRET, // Secondary web app secret
        process.env.FACEBOOK_IOS_CLIENT_SECRET, // iOS app secret
        process.env.FACEBOOK_ANDROID_CLIENT_SECRET, // Android app secret
      ],
      scopes: ["email", "public_profile"], // Requested scopes
    }),
    // Other plugins...
  ],
});
```

### Client-Side Setup

Import the client-side companion to ensure type safety:

```typescript
import { createAuthClient } from "better-auth/react";
import { facebookPolyAuthClient } from "facebook-polyauth/client";

export const authClient = createAuthClient({
  // Other Better Auth client options...

  plugins: [
    facebookPolyAuthClient(),
    // Other client plugins...
  ],
});
```

### Sign In with Facebook

```typescript
const signIn = async () => {
  const data = await authClient.signIn.social({
    provider: "facebook"
  });
};
```

### Sign In with Facebook ID Token

```typescript
const signInWithIdToken = async (idToken: string) => {
  const data = await authClient.signIn.social({
    provider: "facebook",
    idToken: {
      token: idToken
    }
  });
};
```

## API Reference

### Server Plugin Options

The `facebookPolyAuth` function accepts all standard Facebook provider options plus:

- `additionalClientIds`: Array of additional client IDs to validate tokens against
- `additionalClientSecrets`: Array of client secrets corresponding to additional client IDs (required for token validation)

### How It Works

1. When a Facebook ID token is received, it first attempts validation with the primary client ID
2. If that fails and additional client IDs are configured, it validates using Facebook's debug_token endpoint
3. For each additional client ID, it:
   - Gets an app access token using the client ID and secret
   - Uses the app access token to validate the user token via debug_token endpoint
   - Checks if the token is valid and belongs to the correct app
4. If validation succeeds with any client ID, authentication proceeds

## Important Notes

- **Client Secrets Required**: Unlike Google's token validation, Facebook requires client secrets for token validation, so you must provide `additionalClientSecrets` corresponding to each `additionalClientId`
- **Security**: Keep client secrets secure and never expose them in client-side code
- **Rate Limits**: Facebook has rate limits on API calls, including the debug_token endpoint

## Use Cases

- **Multi-Application Suites** - Support authentication across a suite of related web applications
- **White-Labeled Applications** - Enable authentication for multiple branded versions of your product
- **Cross-Platform Applications** - Seamless sign-in across web and mobile platforms
- **Development Environments** - Support different client IDs for your deployment pipeline

## Facebook App Configuration

Make sure to set the redirect URL to match your application:
- Local development: `http://localhost:3000/api/auth/callback/facebook`
- Production: `https://yourdomain.com/api/auth/callback/facebook`

If you change the base path of the auth routes, update the redirect URL accordingly.
