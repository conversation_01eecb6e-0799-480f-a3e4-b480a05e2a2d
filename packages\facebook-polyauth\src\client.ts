import { BetterAuthClientPlugin } from "better-auth/client";
import type { facebookPolyAuth } from "./server.js";

type FacebookPolyAuthPlugin = typeof facebookPolyAuth;

/**
 * Client-side companion for the facebook-polyauth plugin.
 * This allows type-safe integration between the server and client plugins.
 */
export const facebookPolyAuthClient = () => {
  return {
    id: "facebook-polyauth",
    $InferServerPlugin: {} as ReturnType<FacebookPolyAuthPlugin>,
  } satisfies BetterAuthClientPlugin;
};
