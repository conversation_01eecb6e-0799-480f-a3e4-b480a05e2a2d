//accountpage
import { createFileRoute } from '@tanstack/react-router'
import { useSession } from '@/lib/auth-client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { User, Crown, Zap, X } from 'lucide-react'
import { LogOutButton } from '@/components/auth/logout-button'
import { useCustomer } from 'autumn-js/react'
import { useState, useEffect } from 'react'
import { Sheet } from 'react-modal-sheet'

export const Route = createFileRoute('/account/')({
  component: AccountPage,
})

// Mobile detection hook
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
}

function AccountPage() {
  const { data: session, isPending } = useSession()
  const { customer } = useCustomer()
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false)
  const isMobile = useIsMobile()

  // Current path for navigation highlighting
  const currentPath = "/account"

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  const user = session.user
  const userInitials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.charAt(0).toUpperCase() || 'U'

  // Get subscription data from Autumn
  const currentProduct = customer?.products?.[0] // Get first active product
  const messagesFeature = customer?.features ? Object.values(customer.features).find((f: any) => f.feature_id === 'messages') : null
  const messagesUsed = messagesFeature ? ((messagesFeature as any).limit - (messagesFeature.balance || 0)) : 0
  const messagesLimit = (messagesFeature as any)?.limit || 50

  const handleUpgrade = async (plan: string, billing: string = 'monthly') => {
    try {
      // Navigate to checkout with plan parameters
      window.location.href = `/account/billing/checkout?plan=${plan}&billing=${billing}`
    } catch (error) {
      console.error('Error starting upgrade process:', error)
    }
  }

  return (
    <div className="w-full min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16] py-0 md:py-8 flex items-center justify-center">
      {/* Main container - full width on mobile, constrained on desktop */}
      <div className="w-full min-h-screen md:min-h-[88vh] md:max-w-md lg:max-w-lg xl:max-w-xl md:rounded-xl md:shadow-lg relative md:overflow-hidden">

        {/* Navigation bar - Settings header */}
        <div className="fixed top-0 left-0 right-0 h-20 backdrop-blur-sm justify-between px-4 z-[100005] items-center flex md:absolute md:top-0">
          <div className="font-manrope_1 flex gap-4 items-center">
            <span className="text-black dark:text-white text-base font-manrope_1 font-bold">Settings</span>
            {/* Hide subtext on mobile */}
            <span className="hidden md:block text-sm text-[#7e7b76] font-manrope_1">
              Manage your account and billing
            </span>
          </div>
          <button
            onClick={() => setIsBottomSheetOpen(true)}
            className="flex items-center gap-2"
          >
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.image || ''} alt={user.name || ''} />
              <AvatarFallback>{userInitials}</AvatarFallback>
            </Avatar>
          </button>
        </div>

        {/* Main content container - positioned at bottom and scrollable */}
        <div className="absolute top-20 bottom-0 left-0 right-0 z-10 overflow-y-auto">
          <div className="px-4 pb-4 pt-4">
            {/* Header text */}
           

            {/* Settings content section */}
            <div className="w-full border-b mb-4 border-black/20"/>
            <div className="w-full mx-auto">

              {/* Settings cards content */}
              <div className="space-y-6">

                  {/* Basic Information Card */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
                        Basic Information
                      </CardTitle>
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.image || ''} alt={user.name || ''} />
                        <AvatarFallback className="text-sm">{userInitials}</AvatarFallback>
                      </Avatar>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex w-full justify-between">
                        <p className="font-manrope_1 text-sm font-bold text-black dark:text-white">Name</p>
                        <p className="font-manrope_1 text-sm text-black/60 dark:text-white/60">{user.firstName} {user.lastName}</p>
                      </div>
                      <div className="flex w-full justify-between">
                        <p className="font-manrope_1 text-sm font-bold text-black dark:text-white">Email</p>
                        <p className="font-manrope_1 text-sm text-black/60 dark:text-white/60">{user.email}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Account & Subscription Card */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader>
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">Account</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Current Plan</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">{currentProduct?.name || 'Free Plan'}</p>
                      </div>

                      <div className="space-y-2">
                        <Button
                          onClick={() => handleUpgrade('foundation_plan')}
                          className="w-full bg-gray-900 text-white hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100"
                        >
                          Foundation Plan - $9.99/month
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => handleUpgrade('performance_plan')}
                          className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                        >
                          Performance Plan - $19.99/month
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => handleUpgrade('champion_plan')}
                          className="w-full border-yellow-400 text-yellow-700 hover:bg-yellow-50 dark:border-yellow-500 dark:text-yellow-400 dark:hover:bg-yellow-900/20"
                        >
                          Champion Plan - $29.99/month
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Usage Card */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader>
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">Usage</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Usage (Last 30 days)</p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Premium models</span>
                              <Crown className="w-4 h-4 text-gray-400" />
                            </div>
                            <div className="text-lg font-semibold text-gray-900 dark:text-white">{messagesUsed} / {messagesLimit}</div>
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min((messagesUsed / messagesLimit) * 100, 100)}%` }}
                              ></div>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              You've used {messagesUsed} requests out of your {messagesLimit} fast requests quota.
                            </p>
                          </div>

                          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Free models</span>
                              <Zap className="w-4 h-4 text-gray-400" />
                            </div>
                            <div className="text-lg font-semibold text-gray-900 dark:text-white">0 / 500</div>
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                              <div className="bg-green-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              You've used no requests out of your 500 monthly fast requests quota.
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Sheet for user options - Responsive positioning */}
        <div className={`${!isMobile ? 'absolute inset-0' : ''}`}>
          <Sheet
            isOpen={isBottomSheetOpen}
            onClose={() => setIsBottomSheetOpen(false)}
            detent="content-height"
            className="md:max-w-md lg:max-w-lg xl:max-w-xl md:fixed md:mb-12 md:rounded-b-xl md:bottom-0 md:left-0 md:right-0 mx-auto"
          >
            <Sheet.Container className={!isMobile ? 'relative !fixed-none' : ''}>
              <Sheet.Header>
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-manrope_1">
                    Account Options
                  </h3>
                  <button
                    onClick={() => setIsBottomSheetOpen(false)}
                    className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
              </Sheet.Header>
              <Sheet.Content>
                <div className="pb-8">
                  {/* User info section */}
                  <div className="px-4 py-4">
                    <div className="font-manrope_1 flex gap-4 items-center mb-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.image || ''} alt={user.name || ''} />
                        <AvatarFallback>{userInitials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <span className="text-black dark:text-white text-base font-manrope_1 font-bold block">{user.name}</span>
                        <span className="text-[#7e7b76] text-sm font-manrope_1">{user.email}</span>
                      </div>
                    </div>

                    {/* Logout button */}
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <LogOutButton
                        variant="ghost"
                        className="w-full justify-start text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm md:text-base font-manrope_1"
                      />
                    </div>
                  </div>
                </div>
              </Sheet.Content>
            </Sheet.Container>
            <Sheet.Backdrop
              onClick={() => setIsBottomSheetOpen(false)}
              className={!isMobile ? 'absolute inset-0' : ''}
            />
          </Sheet>
        </div>

      </div>
  )
}