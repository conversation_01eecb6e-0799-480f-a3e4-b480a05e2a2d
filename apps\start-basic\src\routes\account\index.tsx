//accountpage
import { createFileRoute } from '@tanstack/react-router'
import { useSession } from '@/lib/auth-client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { User, Crown, Zap } from 'lucide-react'
import { LogOutButton } from '@/components/auth/logout-button'
import { useCustomer } from 'autumn-js/react'

export const Route = createFileRoute('/account/')({
  component: AccountPage,
})

function AccountPage() {
  const { data: session, isPending } = useSession()
  const { customer } = useCustomer()
  
  // Current path for navigation highlighting
  const currentPath = "/account"

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  const user = session.user
  const userInitials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.charAt(0).toUpperCase() || 'U'

  // Get subscription data from Autumn
  const currentProduct = customer?.products?.[0] // Get first active product
  const messagesFeature = customer?.features ? Object.values(customer.features).find((f: any) => f.feature_id === 'messages') : null
  const messagesUsed = messagesFeature ? ((messagesFeature as any).limit - (messagesFeature.balance || 0)) : 0
  const messagesLimit = (messagesFeature as any)?.limit || 50

  const handleUpgrade = async (plan: string, billing: string = 'monthly') => {
    try {
      // Navigate to checkout with plan parameters
      window.location.href = `/account/billing/checkout?plan=${plan}&billing=${billing}`
    } catch (error) {
      console.error('Error starting upgrade process:', error)
    }
  }

  return (
    <div className="w-full min-h-screen relative">
      {/* Add the Navigation component */}
 <div className="fixed h-20 top-0 md:top-7 right-0 left-0 md:left-3 md:right-4 w-full md:w-[38%] backdrop-blur-sm justify-between px-4 z-[100005] items-center gap-2 py-1">
        
        <div className="flex items-center justify-between">
        <div className="font-manrope_1 flex gap-4 items-center">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.image || ''} alt={user.name || ''} />
            <AvatarFallback>{userInitials}</AvatarFallback>
          </Avatar>
          <span className="text-black dark:text-white  text-sm md:text-base font-manrope_1 font-bold">{user.name}</span>
        </div>
        <LogOutButton className="text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm md:text-base font-manrope_1" />
        </div>
        </div>
           
      <div className="w-full h-full max-w-[1600px] mx-auto px-4 md:px-8 py-12 lg:py-8 flex flex-col lg:flex-row items-start gap-12 lg:gap-8 relative z-10">
        {/* Spacer div for fixed sidebar on larger screens */}
        <div className="hidden lg:block lg:w-2/4 lg:max-w-[30%]"></div>

        {/* Sidebar - fixed on larger screens */}
          <div className="w-full lg:w-2/4 text-left mt-4 lg:mt-[10rem] lg:fixed lg:max-w-[34%]">
          <div className="story-margin" id="settings-content">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-black dark:from-white via-gray-700 dark:via-gray-300 to-gray-600 dark:to-gray-400 py-4 font-manrope_1">
              Settings
            </h1>
            <p className="text-sm md:text-lg text-[#7e7b76] font-manrope_1 max-w-full mx-auto lg:mx-0">
              You can manage your account, billing, and referrals from this page
            </p>
            
         
          </div>
        </div>

        {/* Main content */}
        <div className="w-full lg:w-3/4 relative lg:pr-0 lg:flex lg:flex-col lg:items-end lg:z-30 ">
          {/* Settings content in card format similar to PreOnboardingSteps */}
          <div className="lg:w-[54vw] lg:float-right lg:mr-0 relative lg:max-h-[calc(100vh-0px)]   rounded-xl shadow-md dark:shadow-white/10">
            
            {/* Settings content wrapper */}
            <div className="w-full overflow-hidden rounded-xl">
              {/* Header section similar to PreOnboardingSteps */}
              <div className="flex items-center gap-4 px-6 md:px-8 py-3 bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-t-xl">
                <div className="text-[#7e7b76] text-xs text-left md:text-sm font-manrope_1 italic">
                  Manage your account settings and subscription
                </div>
              </div>

              {/* Main settings content */}
              <div className="bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-b-xl">
                <div className="p-6 md:p-8 space-y-6">
                  
                  {/* Basic Information Card */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
                        Basic Information
                      </CardTitle>
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.image || ''} alt={user.name || ''} />
                        <AvatarFallback className="text-sm">{userInitials}</AvatarFallback>
                      </Avatar>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex w-full justify-between">
                        <p className="font-manrope_1 text-sm font-bold text-black dark:text-white">Name</p>
                        <p className="font-manrope_1 text-sm text-black/60 dark:text-white/60">{user.firstName} {user.lastName}</p>
                      </div>
                      <div className="flex w-full justify-between">
                        <p className="font-manrope_1 text-sm font-bold text-black dark:text-white">Email</p>
                        <p className="font-manrope_1 text-sm text-black/60 dark:text-white/60">{user.email}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Account & Subscription Card */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader>
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">Account</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Current Plan</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">{currentProduct?.name || 'Free Plan'}</p>
                      </div>

                      <div className="space-y-2">
                        <Button
                          onClick={() => handleUpgrade('foundation_plan')}
                          className="w-full bg-gray-900 text-white hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100"
                        >
                          Foundation Plan - $9.99/month
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => handleUpgrade('performance_plan')}
                          className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                        >
                          Performance Plan - $19.99/month
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => handleUpgrade('champion_plan')}
                          className="w-full border-yellow-400 text-yellow-700 hover:bg-yellow-50 dark:border-yellow-500 dark:text-yellow-400 dark:hover:bg-yellow-900/20"
                        >
                          Champion Plan - $29.99/month
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Usage Card */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader>
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">Usage</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Usage (Last 30 days)</p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Premium models</span>
                              <Crown className="w-4 h-4 text-gray-400" />
                            </div>
                            <div className="text-lg font-semibold text-gray-900 dark:text-white">{messagesUsed} / {messagesLimit}</div>
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min((messagesUsed / messagesLimit) * 100, 100)}%` }}
                              ></div>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              You've used {messagesUsed} requests out of your {messagesLimit} fast requests quota.
                            </p>
                          </div>

                          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Free models</span>
                              <Zap className="w-4 h-4 text-gray-400" />
                            </div>
                            <div className="text-lg font-semibold text-gray-900 dark:text-white">0 / 500</div>
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                              <div className="bg-green-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              You've used no requests out of your 500 monthly fast requests quota.
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}