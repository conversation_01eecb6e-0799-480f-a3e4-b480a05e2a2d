// Example: Complete setup using Google, Instagram, and Facebook PolyAuth together

import { betterAuth } from "better-auth";
import { googlePolyAuth } from "google-polyauth/server";
import { instagramPolyAuth } from "instagram-polyauth/server";
import { facebookPolyAuth } from "facebook-polyauth/server";

// Server-side auth configuration
export const auth = betterAuth({
  database: {
    // Your Convex or other database configuration
  },
  
  plugins: [
    // Google PolyAuth
    googlePolyAuth({
      clientId: process.env.GOOGLE_PRIMARY_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      additionalClientIds: [
        process.env.GOOGLE_WEB2_CLIENT_ID!,
        process.env.GOOGLE_IOS_CLIENT_ID!,
        process.env.GOOGLE_ANDROID_CLIENT_ID!,
      ],
      scope: ["openid", "profile", "email"],
    }),
    
    // Instagram PolyAuth
    instagramPolyAuth({
      clientId: process.env.INSTAGRAM_PRIMARY_CLIENT_ID!,
      clientSecret: process.env.INSTAGRAM_CLIENT_SECRET!,
      additionalClientIds: [
        process.env.INSTAGRAM_WEB2_CLIENT_ID!,
        process.env.INSTAGRAM_IOS_CLIENT_ID!,
        process.env.INSTAGRAM_ANDROID_CLIENT_ID!,
      ],
      scopes: ["user_profile", "user_media"],
    }),
    
    // Facebook PolyAuth
    facebookPolyAuth({
      clientId: process.env.FACEBOOK_PRIMARY_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
      additionalClientIds: [
        process.env.FACEBOOK_WEB2_CLIENT_ID!,
        process.env.FACEBOOK_IOS_CLIENT_ID!,
        process.env.FACEBOOK_ANDROID_CLIENT_ID!,
      ],
      additionalClientSecrets: [
        process.env.FACEBOOK_WEB2_CLIENT_SECRET!,
        process.env.FACEBOOK_IOS_CLIENT_SECRET!,
        process.env.FACEBOOK_ANDROID_CLIENT_SECRET!,
      ],
      scopes: ["email", "public_profile"],
    }),
    
    // Other plugins...
  ],
});

// Client-side auth configuration
import { createAuthClient } from "better-auth/react";
import { googlePolyAuthClient } from "google-polyauth/client";
import { instagramPolyAuthClient } from "instagram-polyauth/client";
import { facebookPolyAuthClient } from "facebook-polyauth/client";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3000",
  
  plugins: [
    googlePolyAuthClient(),
    instagramPolyAuthClient(),
    facebookPolyAuthClient(),
    // Other client plugins...
  ],
});

// Utility functions for each provider
export const socialSignIn = {
  google: async () => {
    return await authClient.signIn.social({
      provider: "google",
      callbackURL: "/dashboard",
    });
  },
  
  instagram: async () => {
    return await authClient.signIn.social({
      provider: "instagram",
      callbackURL: "/dashboard",
    });
  },
  
  facebook: async () => {
    return await authClient.signIn.social({
      provider: "facebook",
      callbackURL: "/dashboard",
    });
  },
};

// Example React component using all providers
export const SocialLoginButtons = () => {
  const handleSocialLogin = async (provider: 'google' | 'instagram' | 'facebook') => {
    try {
      await socialSignIn[provider]();
    } catch (error) {
      console.error(`${provider} login failed:`, error);
    }
  };

  return (
    <div className="space-y-3">
      <button
        onClick={() => handleSocialLogin('google')}
        className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        Continue with Google
      </button>
      
      <button
        onClick={() => handleSocialLogin('instagram')}
        className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
      >
        Continue with Instagram
      </button>
      
      <button
        onClick={() => handleSocialLogin('facebook')}
        className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
      >
        Continue with Facebook
      </button>
    </div>
  );
};
