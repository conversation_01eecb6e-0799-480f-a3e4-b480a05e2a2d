//checkoutpage
import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useState } from 'react'
import { useSession } from '@/lib/auth-client'
import { Link } from '@tanstack/react-router'
import { ArrowLeft, Crown, Zap, Target } from 'lucide-react'
import { useAutumn } from 'autumn-js/react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { LogOutButton } from '@/components/auth/logout-button'

export const Route = createFileRoute('/account/billing/checkout/')({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      plan: (search.plan as string) || 'foundation_plan',
      billing: (search.billing as string) || 'monthly',
      from: (search.from as string),
      success_url: (search.success_url as string),
      cancel_url: (search.cancel_url as string),
    }
  },
  component: CheckoutPage,
})

function CheckoutPage() {
  const searchParams = useSearch({ from: '/account/billing/checkout/' }) as any
  const { plan, billing, from, success_url, cancel_url } = searchParams
  // auth_provider is available for future use to customize checkout flow
  const { data: session, isPending } = useSession()
  const { attach } = useAutumn()
  const [isLoading, setIsLoading] = useState(false)

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  const user = session.user
  const userInitials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.charAt(0).toUpperCase() || 'U'

  // Plan configurations matching database and Autumn setup
  const plans = {
    'foundation_plan': {
      name: 'Foundation Plan',
      icon: Target,
      monthlyPrice: 9.99,
      yearlyPrice: 101.90,
      description: 'Build your fitness foundation with intelligent AI guidance',
      features: [
        'Unlimited workout plans',
        '15 meal scans per month',
        'Form guidance with AI feedback',
        '3 progress photos per month',
        '5 meal planning sessions per month'
      ]
    },
    'performance_plan': {
      name: 'Performance Plan',
      icon: Zap,
      monthlyPrice: 19.99,
      yearlyPrice: 203.90,
      description: 'Unlock advanced AI coaching for serious fitness growth',
      features: [
        'Everything from Foundation Plan',
        'Unlimited meal scans',
        'Advanced form guidance',
        'Unlimited progress photos',
        'Unlimited nutrition coaching',
        'Unlimited meal planning'
      ]
    },
    'champion_plan': {
      name: 'Champion Plan',
      icon: Crown,
      monthlyPrice: 29.99,
      yearlyPrice: 305.90,
      description: 'Elite AI coaching that anticipates your every fitness need',
      features: [
        'Everything from Performance Plan',
        'Unlimited AI coaching sessions',
        'Advanced behavioral learning',
        'Predictive workout adjustments',
        'Priority support'
      ]
    }
  }

  const selectedPlan = plans[plan as keyof typeof plans] || plans['foundation_plan']
  const isYearly = billing === 'yearly'
  const price = isYearly ? selectedPlan.yearlyPrice : selectedPlan.monthlyPrice
  const interval = isYearly ? 'year' : 'month'
  const IconComponent = selectedPlan.icon

  const handleCheckout = async () => {
    setIsLoading(true)
    try {
      console.log('Starting Autumn checkout for plan:', plan)

      // Use Autumn to handle the subscription
      await attach({
        productId: plan,
        // If coming from mobile, handle success/cancel URLs
        ...(from === 'mobile' && success_url && {
          onSuccess: () => {
            if (success_url) {
              window.location.href = decodeURIComponent(success_url)
            }
          }
        }),
        ...(from === 'mobile' && cancel_url && {
          onCancel: () => {
            if (cancel_url) {
              window.location.href = decodeURIComponent(cancel_url)
            }
          }
        })
      })

      // If not from mobile, redirect to account page
      if (from !== 'mobile') {
        window.location.href = '/account?success=true'
      }

    } catch (error) {
      console.error('Checkout error:', error)
      alert('Error starting checkout process')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full min-h-screen relative">
      {/* Add the Navigation component */}
      <div className="fixed h-20 top-0 md:top-7 right-0 left-0 md:left-3 md:right-4 w-full md:w-[38%] backdrop-blur-sm justify-between px-4 z-[100005] items-center gap-2 py-1">
        
        <div className="flex items-center justify-between">
        <div className="font-manrope_1 flex gap-4 items-center">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.image || ''} alt={user.name || ''} />
            <AvatarFallback>{userInitials}</AvatarFallback>
          </Avatar>
          <span className="text-black dark:text-white text-sm md:text-base font-manrope_1 font-bold">{user.name}</span>
        </div>
        <LogOutButton className="text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm md:text-base font-manrope_1" />
        </div>
          <Link
              to="/account"
              className="flex items-center text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm md:text-base font-manrope_1 mt-2"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Account
            </Link>
        
      </div>
   
           
      <div className="w-full h-full max-w-[1600px] mx-auto px-4 md:px-8 py-12 lg:py-8 flex flex-col lg:flex-row items-start gap-12 lg:gap-8 relative z-10">
        {/* Spacer div for fixed sidebar on larger screens */}
        <div className="hidden lg:block lg:w-2/4 lg:max-w-[30%]"></div>

        {/* Sidebar - fixed on larger screens */}
        <div className="w-full lg:w-2/4 text-left mt-4 lg:mt-[10rem] lg:fixed lg:max-w-[34%]">
          <div className="story-margin" id="checkout-content">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-black dark:from-white via-gray-700 dark:via-gray-300 to-gray-600 dark:to-gray-400 py-4 font-manrope_1">
              Checkout
            </h1>
            <p className="text-sm md:text-lg text-[#7e7b76] font-manrope_1 max-w-full mx-auto lg:mx-0">
              Complete your subscription to unlock premium AI coaching features
            </p>
            
          
          </div>
        </div>

        {/* Main content */}
        <div className="w-full lg:w-3/4 relative lg:pr-0 lg:flex lg:flex-col lg:items-end lg:z-30">
          {/* Checkout content in card format similar to PreOnboardingSteps */}
          <div className="lg:w-[54vw] lg:float-right lg:mr-0 relative lg:max-h-[calc(100vh-0px)] rounded-xl shadow-md dark:shadow-white/10">
            
            {/* Checkout content wrapper */}
            <div className="w-full overflow-hidden rounded-xl">
              {/* Header section similar to PreOnboardingSteps */}
              <div className="flex items-center gap-4 px-6 md:px-8 py-3 bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-t-xl">
                <div className="text-[#7e7b76] text-xs text-left md:text-sm font-manrope_1 italic">
                  {from === 'mobile' && (
                    'You\'ll be redirected back to the mobile app after completing your subscription.'
                  )}
                  {!from && 'Complete your subscription to access premium features'}
                </div>
              </div>

              {/* Main checkout content */}
              <div className="bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-b-xl">
                <div className="p-6 md:p-8 space-y-6">
                  
                  {/* Plan Details */}
                  <div className="mb-8">
                    <div className="flex items-center mb-4">
                      <IconComponent className="w-8 h-8 mr-3" style={{ color: '#3b82f6' }} />
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-manrope_1">{selectedPlan.name}</h2>
                        <p className="text-[#7e7b76] font-manrope_1">{selectedPlan.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* Order Summary Card */}
                  <div className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f] rounded-lg p-6">
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-manrope_1">Order Summary</h2>

                    <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white font-manrope_1">{selectedPlan.name}</h3>
                          <p className="text-sm text-[#7e7b76] font-manrope_1">Billed {interval}ly</p>
                          {isYearly && (
                            <p className="text-sm text-green-600 font-medium font-manrope_1">Save 15% with yearly billing</p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-gray-900 dark:text-white font-manrope_1">
                            ${price.toFixed(2)}
                          </p>
                          <p className="text-sm text-[#7e7b76] font-manrope_1">per {interval}</p>
                          {isYearly && (
                            <p className="text-xs text-[#7e7b76] font-manrope_1">
                              ${(price / 12).toFixed(2)}/month
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-manrope_1">Included features:</h4>
                        <ul className="space-y-1">
                          {selectedPlan.features.map((feature, index) => (
                            <li key={index} className="flex items-start text-sm text-[#7e7b76] font-manrope_1">
                              <svg className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-lg font-medium text-gray-900 dark:text-white font-manrope_1">Total</span>
                        <span className="text-2xl font-bold text-gray-900 dark:text-white font-manrope_1">
                          ${price.toFixed(2)}/{interval}
                        </span>
                      </div>

                      <button
                        onClick={handleCheckout}
                        disabled={isLoading}
                        className="w-full bg-gray-900 text-white hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100 py-3 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-manrope_1"
                      >
                        {isLoading ? 'Processing...' : `Start 3-Day Free Trial`}
                      </button>
                    </div>
                  </div>

                  {/* Footer text */}
                  <p className="text-sm font-manrope_1 text-[#7e7b76] text-center">
                    3-day free trial included. <br/>You can cancel your subscription at any time. No long-term commitments.
                  </p>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}