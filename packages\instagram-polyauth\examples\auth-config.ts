// Example: How to use Instagram PolyAuth in your Better Auth configuration

import { betterAuth } from "better-auth";
import { instagramPolyAuth } from "instagram-polyauth/server";

export const auth = betterAuth({
  database: {
    // Your database configuration
  },
  
  plugins: [
    instagramPolyAuth({
      clientId: process.env.INSTAGRAM_PRIMARY_CLIENT_ID!, // Primary client ID
      clientSecret: process.env.INSTAGRAM_CLIENT_SECRET!, // Client secret
      additionalClientIds: [
        process.env.INSTAGRAM_WEB2_CLIENT_ID!, // Secondary web app
        process.env.INSTAGRAM_IOS_CLIENT_ID!, // iOS app
        process.env.INSTAGRAM_ANDROID_CLIENT_ID!, // Android app
      ],
      scopes: ["user_profile", "user_media"], // Requested scopes
      
      // Optional: Custom user info fetching
      getUserInfo: async (tokens) => {
        const response = await fetch(
          `https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${tokens.access_token}`
        );
        
        if (!response.ok) {
          throw new Error("Failed to fetch Instagram user info");
        }
        
        const userInfo = await response.json();
        
        return {
          id: userInfo.id,
          name: userInfo.username,
          email: null, // Instagram doesn't provide email
          image: null, // Would need additional API call
          emailVerified: false,
        };
      },
      
      // Optional: Custom profile mapping
      mapProfileToUser: async (profile) => {
        return {
          firstName: profile.name || profile.username,
          lastName: "",
        };
      },
    }),
    
    // Other plugins...
  ],
});
